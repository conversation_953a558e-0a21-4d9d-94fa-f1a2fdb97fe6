import { useQuery, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useAuth } from './use-auth';
import { useWallet } from './use-wallet';

// Utility function for formatting DasWos coins balance
export const formatDasWosCoins = (balance: number): string => {
  return balance.toLocaleString('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  });
};

/**
 * Hook to manage DasWos coins balance (user-owned balance system)
 * Balance is stored in user account, wallet is used as interface
 */
export function useDasWosCoins() {
  const { user } = useAuth();
  const { wallet } = useWallet();
  const queryClient = useQueryClient();

  // Fetch user's coins balance (only when user is authenticated AND wallet is connected)
  const { data, isLoading, error } = useQuery({
    queryKey: ['/api/user/daswos-coins/balance', wallet?.wallet_id], // Removed Date.now() to prevent infinite queries
    queryFn: async () => {
      if (!wallet?.wallet_id) {
        throw new Error('Wallet connection required to access balance');
      }

      const url = `/api/user/daswos-coins/balance?wallet_id=${encodeURIComponent(wallet.wallet_id)}&_t=${Date.now()}`;

      return apiRequest(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache', // Force fresh request
          'Pragma': 'no-cache'
        }
      });
    },
    enabled: !!user && !!wallet?.wallet_id, // Only fetch when user is logged in AND wallet is connected
    staleTime: 30000, // 30 seconds cache
    retry: (failureCount, error: any) => {
      // Don't retry on security errors
      if (error?.status === 403) return false;
      return failureCount < 3;
    }
  });

  // Function to refresh the balance
  const refreshBalance = () => {
    queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });
  };

  // Function to purchase coins (adds to user account)
  const purchaseCoins = async (amount: number) => {
    if (!user) {
      throw new Error('You must be logged in to purchase DasWos Coins');
    }

    if (!wallet?.wallet_id) {
      throw new Error('You must connect your wallet to purchase DasWos Coins');
    }

    try {
      const response = await apiRequest('/api/user/daswos-coins/purchase', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          wallet_id: wallet.wallet_id, // REQUIRED: User's wallet ID for security verification
          metadata: {
            packageName: `${amount} DasWos Coins`,
            purchaseTimestamp: new Date().toISOString(),
            walletUsed: wallet.wallet_id
          }
        }),
      });

      // Refresh the balance after purchase
      refreshBalance();

      return response;
    } catch (error) {
      console.error('Error purchasing DasWos coins:', error);
      throw error;
    }
  };

  // Function to spend coins (requires user auth + wallet connection for security)
  const spendCoins = async (amount: number, description: string) => {
    if (!user) {
      throw new Error('You must be logged in to spend DasWos Coins');
    }

    if (!wallet?.wallet_id) {
      throw new Error('You must connect your wallet to spend DasWos Coins');
    }

    try {
      const response = await apiRequest('/api/user/daswos-coins/spend', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          description,
          wallet_id: wallet.wallet_id, // REQUIRED: User's wallet ID for security verification
          metadata: {
            spendTimestamp: new Date().toISOString(),
            walletUsed: wallet.wallet_id
          }
        }),
      });

      // Refresh the balance after spending
      refreshBalance();

      return response;
    } catch (error) {
      console.error('Error spending DasWos coins:', error);
      throw error;
    }
  };

  return {
    balance: data?.balance || 0,
    formattedBalance: formatDasWosCoins(data?.balance || 0),
    isLoading,
    error,
    refreshBalance,
    purchaseCoins,
    spendCoins,
    // Status indicators
    isUserAuthenticated: !!user,
    isWalletConnected: !!wallet,
    canPurchase: !!user && !!wallet, // Both user auth AND wallet required for purchase
    canSpend: !!user && !!wallet, // Both user auth AND wallet required for spending
    // Wallet access information
    walletAccess: data?.wallet_access || null,
    recommendations: data?.recommendations || [],
    walletInfo: wallet ? {
      id: wallet.id,
      wallet_id: wallet.wallet_id,
      connected: true
    } : null
  };
}
