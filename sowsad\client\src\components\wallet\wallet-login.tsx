import React, { useState } from 'react';
import { useLocation } from 'wouter';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Wallet, Lock, User, ArrowLeft } from 'lucide-react';
import { useWallet } from '@/hooks/use-wallet';
import { useAuth } from '@/hooks/use-auth';

interface WalletLoginProps {
  onClose?: () => void;
}

export default function WalletLogin({ onClose }: WalletLoginProps) {
  const [, setLocation] = useLocation();
  const { loginToWallet, createWallet, isLoading } = useWallet();
  const { user } = useAuth();

  // Check if user has an assigned DasWos wallet
  const userWalletId = user?.walletId;
  const hasAssignedWallet = Boolean(userWalletId);

  // Login form state
  const [loginForm, setLoginForm] = useState({
    walletId: userWalletId || '',
    password: ''
  });
  const [loginError, setLoginError] = useState('');
  const [loginSuccess, setLoginSuccess] = useState('');

  // Create wallet form state - pre-fill with user's assigned wallet ID if available
  const [createForm, setCreateForm] = useState({
    walletId: userWalletId || '',
    password: '',
    confirmPassword: ''
  });
  const [createError, setCreateError] = useState('');
  const [createSuccess, setCreateSuccess] = useState('');

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError('');
    setLoginSuccess('');

    try {
      const result = await loginToWallet(loginForm.walletId, loginForm.password);

      if (result.success) {
        setLoginSuccess('Wallet access successful! Redirecting...');
        setTimeout(() => {
          if (onClose) {
            onClose();
          } else {
            setLocation('/');
          }
        }, 1500);
      } else {
        setLoginError(result.error || 'Invalid wallet ID or password');
      }
    } catch (error) {
      console.error('Wallet login error:', error);
      setLoginError('Login failed. Please try again.');
    }
  };

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setCreateError('');
    setCreateSuccess('');

    if (createForm.password !== createForm.confirmPassword) {
      setCreateError('Passwords do not match');
      return;
    }

    if (createForm.password.length < 6) {
      setCreateError('Password must be at least 6 characters');
      return;
    }

    try {
      const result = await createWallet(createForm.walletId, createForm.password);

      if (result.success) {
        setCreateSuccess('Wallet created successfully! Redirecting...');
        setTimeout(() => {
          if (onClose) {
            onClose();
          } else {
            setLocation('/');
          }
        }, 1500);
      } else {
        setCreateError(result.error || 'Failed to create wallet');
      }
    } catch (error) {
      console.error('Wallet creation error:', error);
      setCreateError('Failed to create wallet. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-[#E0E0E0] flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-white border border-gray-300 shadow-lg">
        <CardHeader className="text-center border-b border-gray-300 pb-4">
          <div className="flex items-center justify-center mb-4">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-black mr-2">daswos</span>
              <div className="w-8 h-8 bg-black relative">
                <div className="absolute top-0.5 left-0.5 w-3.5 h-2.5 bg-white"></div>
                <div className="absolute bottom-0.5 left-0.5 w-2 h-2 bg-white"></div>
              </div>
            </div>
          </div>
          <CardTitle className="text-lg font-medium">Wallet Access Portal</CardTitle>
          <CardDescription className="text-sm text-gray-600">
            Access your DasWos wallet securely
          </CardDescription>
        </CardHeader>

        <CardContent className="p-0">
          <Tabs defaultValue="login" className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-gray-100 rounded-none">
              <TabsTrigger
                value="login"
                className="data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-black rounded-none"
              >
                Login to Wallet
              </TabsTrigger>
              <TabsTrigger
                value="create"
                className="data-[state=active]:bg-white data-[state=active]:border-b-2 data-[state=active]:border-black rounded-none"
              >
                Create Wallet
              </TabsTrigger>
            </TabsList>

            <div className="p-6">
              {/* User Wallet Info */}
              {hasAssignedWallet ? (
                <Alert className="mb-4 bg-blue-50 border-blue-300">
                  <Wallet className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-sm text-blue-800">
                    <strong>Your DasWos Wallet:</strong> {userWalletId}
                    <br />
                    {user?.username ? `Assigned to: ${user.username}` : 'Ready for password creation'}
                  </AlertDescription>
                </Alert>
              ) : (
                <Alert className="mb-4 bg-gray-50 border-gray-300">
                  <Wallet className="h-4 w-4" />
                  <AlertDescription className="text-sm">
                    <strong>Demo Wallet:</strong> ID: demo-wallet, Password: demo123
                  </AlertDescription>
                </Alert>
              )}

              <TabsContent value="login" className="space-y-4 mt-0">
                <form onSubmit={handleLoginSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="loginWalletId" className="text-sm font-medium">
                      Wallet ID
                    </Label>
                    <div className="relative">
                      <Input
                        id="loginWalletId"
                        type="text"
                        placeholder={hasAssignedWallet ? "Your assigned DasWos wallet" : "Your wallet ID"}
                        value={loginForm.walletId}
                        onChange={(e) => setLoginForm(prev => ({ ...prev, walletId: e.target.value }))}
                        className={`pr-10 border-gray-300 focus:border-blue-500 ${hasAssignedWallet ? 'bg-blue-50 text-blue-900' : ''}`}
                        readOnly={hasAssignedWallet}
                        required
                      />
                      <User className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                    <p className="text-xs text-gray-500">
                      You can use your unique wallet identifier
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="loginPassword" className="text-sm font-medium">
                      Password
                    </Label>
                    <div className="relative">
                      <Input
                        id="loginPassword"
                        type="password"
                        placeholder="Your wallet password"
                        value={loginForm.password}
                        onChange={(e) => setLoginForm(prev => ({ ...prev, password: e.target.value }))}
                        className="pr-10 border-gray-300 focus:border-blue-500"
                        required
                      />
                      <Lock className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                  </div>

                  {loginError && (
                    <Alert className="bg-red-50 border-red-300 text-red-800">
                      <AlertDescription className="text-sm">{loginError}</AlertDescription>
                    </Alert>
                  )}

                  {loginSuccess && (
                    <Alert className="bg-green-50 border-green-300 text-green-800">
                      <AlertDescription className="text-sm">{loginSuccess}</AlertDescription>
                    </Alert>
                  )}

                  <Button
                    type="submit"
                    className="w-full bg-black hover:bg-gray-800 text-white"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Accessing...' : 'Access Wallet'}
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="create" className="space-y-4 mt-0">
                {hasAssignedWallet && (
                  <Alert className="mb-4 bg-green-50 border-green-300">
                    <Wallet className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-sm text-green-800">
                      <strong>Create Password for Your DasWos Wallet</strong>
                      <br />
                      Your wallet ID is already assigned. Just create a secure password to activate it.
                    </AlertDescription>
                  </Alert>
                )}

                <form onSubmit={handleCreateSubmit} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="createWalletId" className="text-sm font-medium">
                      {hasAssignedWallet ? 'Your Assigned Wallet ID' : 'Choose Wallet ID'}
                    </Label>
                    <Input
                      id="createWalletId"
                      type="text"
                      placeholder={hasAssignedWallet ? "Your assigned DasWos wallet" : "Choose a unique wallet ID"}
                      value={createForm.walletId}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, walletId: e.target.value }))}
                      className={`border-gray-300 focus:border-blue-500 ${hasAssignedWallet ? 'bg-blue-50 text-blue-900' : ''}`}
                      readOnly={hasAssignedWallet}
                      required
                    />
                    {hasAssignedWallet && (
                      <p className="text-xs text-blue-600">
                        This wallet ID was automatically assigned to your account during registration.
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="createPassword" className="text-sm font-medium">
                      Password
                    </Label>
                    <Input
                      id="createPassword"
                      type="password"
                      placeholder="Create a secure password"
                      value={createForm.password}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, password: e.target.value }))}
                      className="border-gray-300 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="createConfirmPassword" className="text-sm font-medium">
                      Confirm Password
                    </Label>
                    <Input
                      id="createConfirmPassword"
                      type="password"
                      placeholder="Confirm your password"
                      value={createForm.confirmPassword}
                      onChange={(e) => setCreateForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="border-gray-300 focus:border-blue-500"
                      required
                    />
                  </div>

                  {createError && (
                    <Alert className="bg-red-50 border-red-300 text-red-800">
                      <AlertDescription className="text-sm">{createError}</AlertDescription>
                    </Alert>
                  )}

                  {createSuccess && (
                    <Alert className="bg-green-50 border-green-300 text-green-800">
                      <AlertDescription className="text-sm">{createSuccess}</AlertDescription>
                    </Alert>
                  )}

                  <Button
                    type="submit"
                    className="w-full bg-black hover:bg-gray-800 text-white"
                    disabled={isLoading}
                  >
                    {isLoading ?
                      (hasAssignedWallet ? 'Activating...' : 'Creating...') :
                      (hasAssignedWallet ? 'Activate Wallet' : 'Create Wallet')
                    }
                  </Button>
                </form>
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>

        <div className="border-t border-gray-300 p-4 text-center">
          <Button
            variant="ghost"
            onClick={() => {
              if (onClose) {
                onClose();
              } else {
                setLocation('/');
              }
            }}
            className="text-blue-600 hover:text-blue-800 text-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back to DasWos
          </Button>
        </div>
      </Card>
    </div>
  );
}
