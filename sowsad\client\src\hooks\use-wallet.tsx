import React, { useState, useEffect, createContext, useContext } from 'react';
import { apiRequest } from '@/lib/queryClient';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';

interface Wallet {
  id: string;
  wallet_id: string;
  // REMOVED: balance - balances are now stored in user accounts in main databases
  created_at: string;
  last_accessed: string;
}

interface WalletSession {
  wallet: Wallet;
  session_token: string;
  expires_at: string;
}

interface WalletContextType {
  wallet: Wallet | null;
  isLoading: boolean;
  loginToWallet: (walletId: string, password: string) => Promise<{ success: boolean; error?: string }>;
  createWallet: (walletId: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logoutWallet: () => void;
  refreshWallet: () => Promise<void>;
  isWalletConnected: boolean;
}

const WalletContext = createContext<WalletContextType | undefined>(undefined);

export function useWallet() {
  const context = useContext(WalletContext);
  if (!context) {
    // Return a default implementation when not in context
    return useWalletImplementation();
  }
  return context;
}

function useWalletImplementation(): WalletContextType {
  const [wallet, setWallet] = useState<Wallet | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();
  const { user, isLoading: isUserLoading } = useAuth();

  // Check for existing wallet session on mount and when user changes
  useEffect(() => {
    // Only check wallet session after user authentication has completed loading
    if (!isUserLoading) {
      checkExistingWalletSession();
    }
  }, [user, isUserLoading]); // Re-check when user login state changes or loading completes

  // Function to create wallet connection record
  const createWalletConnection = async (walletId: string, walletUuid: string) => {
    if (!user) return; // Only create connections for logged-in users

    // Skip connection creation for demo wallet to avoid interference
    if (walletId === 'demo-wallet') {
      console.log('Skipping connection creation for demo wallet to maintain isolation');
      return;
    }

    try {
      // Create connection record in wallet database
      const response = await fetch('/api/wallet/create-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          wallet_id: walletUuid,
          wallet_identifier: walletId,
          database_name: 'daswos-18', // This identifies which app is connecting
          user_id: user.id,
          username: user.username,
          is_primary: true // Mark as primary wallet for this user
        }),
      });

      if (!response.ok) {
        console.warn('Failed to create wallet connection record:', await response.text());
      } else {
        console.log('Wallet connection record created successfully');
      }
    } catch (error) {
      console.warn('Error creating wallet connection:', error);
    }
  };

  const checkExistingWalletSession = async () => {
    try {
      const storedWallet = localStorage.getItem('daswos_wallet');
      const storedSession = localStorage.getItem('daswos_wallet_session');

      if (storedWallet && storedSession) {
        const walletData = JSON.parse(storedWallet);
        const sessionData = JSON.parse(storedSession);

        // Check if session is still valid
        if (new Date(sessionData.expires_at) > new Date()) {
          // If user is loaded and authenticated, restore wallet session
          if (user) {
            setWallet(walletData);
            console.log('Restored wallet session:', walletData.wallet_id);
          } else if (!isUserLoading) {
            // User authentication has completed and user is not logged in
            // Clear wallet session for security
            console.log('Clearing wallet session - user not authenticated');
            localStorage.removeItem('daswos_wallet');
            localStorage.removeItem('daswos_wallet_session');
          }
          // If user is null but still loading, don't clear wallet session yet
        } else {
          // Session expired, clear data
          console.log('Wallet session expired, clearing data');
          localStorage.removeItem('daswos_wallet');
          localStorage.removeItem('daswos_wallet_session');
        }
      }
    } catch (error) {
      console.error('Error checking wallet session:', error);
      // Clear invalid data
      localStorage.removeItem('daswos_wallet');
      localStorage.removeItem('daswos_wallet_session');
    }
  };

  const loginToWallet = async (walletId: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);

    try {
      // For demo purposes, check demo wallet - completely local, no database interaction
      if (walletId === 'demo-wallet' && password === 'demo123') {
        // Demo wallet - no balance stored in wallet (balance comes from user account)
        const demoWallet: Wallet = {
          id: 'demo-wallet-uuid',
          wallet_id: 'demo-wallet',
          // REMOVED: balance - balances are now stored in user accounts in main databases
          created_at: new Date().toISOString(),
          last_accessed: new Date().toISOString()
        };

        const sessionData = {
          wallet: demoWallet,
          session_token: 'demo-session-' + Date.now(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
        };

        // Store wallet data
        localStorage.setItem('daswos_wallet', JSON.stringify(demoWallet));
        localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

        setWallet(demoWallet);

        // Skip connection creation for demo wallet to maintain complete isolation
        console.log('Demo wallet login successful - completely isolated from database');

        return { success: true };
      }

      // Make actual API call to wallet database
      try {
        const response = await fetch('/api/wallet/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            walletId,
            password,
            userId: user?.id,
            username: user?.username
          })
        });

        const result = await response.json();

        if (result.success && result.wallet) {
          // Store wallet data locally
          const walletData = {
            id: result.wallet.id,
            wallet_id: result.wallet.wallet_id,
            created_at: result.wallet.created_at,
            last_accessed: result.wallet.last_accessed
          };

          const sessionData = {
            wallet: walletData,
            session_token: result.session?.token,
            expires_at: result.session?.expires_at
          };

          localStorage.setItem('daswos_wallet', JSON.stringify(walletData));
          localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

          setWallet(walletData);

          // Create wallet connection record for tracking
          if (user?.id) {
            await createWalletConnection(walletId, result.wallet.wallet_id);
          }

          return { success: true };
        } else {
          return { success: false, error: result.error || 'Invalid wallet ID or password' };
        }
      } catch (error) {
        console.error('Wallet login error:', error);
        return { success: false, error: 'Login failed. Please try again.' };
      }
    } finally {
      setIsLoading(false);
    }
  };

  const createWallet = async (walletId: string, password: string): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);

    try {
      // Get current user info for the API call
      const userResponse = await fetch('/api/user', {
        credentials: 'include'
      });

      let userId, username;
      if (userResponse.ok) {
        const userData = await userResponse.json();
        userId = userData.id;
        username = userData.username;
      }

      // Call the wallet creation API
      const response = await fetch('/api/wallet/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          walletId,
          password,
          userId,
          username
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        return { success: false, error: errorData.error || 'Failed to create wallet' };
      }

      const result = await response.json();

      if (result.success) {
        // Store wallet data locally
        const walletData = {
          id: result.wallet.id,
          wallet_id: result.wallet.wallet_id,
          created_at: result.wallet.created_at,
          last_accessed: result.wallet.last_accessed
        };

        const sessionData = {
          wallet: walletData,
          session_token: result.session.token,
          expires_at: result.session.expires_at
        };

        localStorage.setItem('daswos_wallet', JSON.stringify(walletData));
        localStorage.setItem('daswos_wallet_session', JSON.stringify(sessionData));

        setWallet(walletData);

        // Create wallet connection record for tracking
        await createWalletConnection(walletId, result.wallet.id);

        return { success: true };
      } else {
        return { success: false, error: 'Failed to create wallet' };
      }

    } catch (error) {
      console.error('Wallet creation error:', error);
      return { success: false, error: 'Failed to create wallet. Please try again.' };
    } finally {
      setIsLoading(false);
    }
  };

  const logoutWallet = async () => {
    const walletId = wallet?.wallet_id;

    // Log wallet disconnect on server if user is authenticated
    if (user && walletId) {
      try {
        await apiRequest('POST', '/api/wallet/disconnect-log', { walletId });
      } catch (error) {
        console.error('Failed to log wallet disconnect:', error);
        // Continue with logout even if logging fails
      }
    }

    // Clear stored wallet data
    localStorage.removeItem('daswos_wallet');
    localStorage.removeItem('daswos_wallet_session');

    // Reset wallet state
    setWallet(null);

    // Invalidate balance queries to prevent stale data
    queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });

    console.log(`🔓 Wallet disconnected: ${walletId || 'unknown'} for user ${user?.username || 'unknown'} (ID: ${user?.id || 'unknown'})`);
  };

  const refreshWallet = async () => {
    if (!wallet) return;

    try {
      // TODO: Replace with actual API call to refresh wallet data
      // const response = await apiRequest(`/api/wallet/${wallet.id}`);
      // setWallet(response.wallet);

      console.log('Wallet refreshed');
    } catch (error) {
      console.error('Error refreshing wallet:', error);
    }
  };

  return {
    wallet,
    isLoading,
    loginToWallet,
    createWallet,
    logoutWallet,
    refreshWallet,
    isWalletConnected: !!wallet
  };
}

// Wallet Provider Component (for when we want to use context)
export function WalletProvider({ children }: { children: React.ReactNode }) {
  const walletImplementation = useWalletImplementation();

  return (
    <WalletContext.Provider value={walletImplementation}>
      {children}
    </WalletContext.Provider>
  );
}

// Utility functions for wallet operations
export const walletUtils = {
  // REMOVED: formatBalance - balances are now managed in user accounts, not wallets

  validateWalletId: (walletId: string): { valid: boolean; error?: string } => {
    if (!walletId || walletId.length < 3) {
      return { valid: false, error: 'Wallet ID must be at least 3 characters' };
    }
    if (walletId.length > 50) {
      return { valid: false, error: 'Wallet ID must be less than 50 characters' };
    }
    if (!/^[a-zA-Z0-9_-]+$/.test(walletId)) {
      return { valid: false, error: 'Wallet ID can only contain letters, numbers, hyphens, and underscores' };
    }
    return { valid: true };
  },

  validatePassword: (password: string): { valid: boolean; error?: string } => {
    if (!password || password.length < 6) {
      return { valid: false, error: 'Password must be at least 6 characters' };
    }
    if (password.length > 128) {
      return { valid: false, error: 'Password must be less than 128 characters' };
    }
    return { valid: true };
  }
};

export type { Wallet, WalletSession, WalletContextType };
