# DasWos Seller Implementation Plan

## 1. User Identification and Session Handling
- Assign a unique `pseudonymous_user_id` for all users, including guests
- Track actions, listings, and trust score progress using this ID
- Link ID to user account upon account creation

## 2. Sell Page UI/UX Components

### Trust Score Dashboard
- Visual progress bar showing current trust score
- Actionable prompts for increasing score:
  - "Sign in or create account" (+30 points)
  - "Start verification" (+30 points)
  - "Complete your first sale" (+5 points)

### Item Listing Form
- Required fields:
  - Title
  - Description
  - Price
  - Quantity
  - Category
  - Tags
  - Shipping options
  - Photos
- Form validation for all required fields

### Guest User Notifications
- Clear warnings about payment limitations for guests
- Benefits of account creation
- Trust score impact of registration

## 3. User Account Requirements
- **Basic Information**:
  - Full name
  - Business name (if applicable)
  - Contact information
  - Address verification
- **Verification**:
  - Email verification
  - Phone verification (optional)

## 4. Trust Score Implementation

### Score Components
- **Account Creation**: +30 points
  - Full name
  - Contact information
  - Address verification
- **Identity Verification**: +30 points
  - Government-issued ID (Director ID for businesses)
  - Proof of address (business address for business accounts)
  - Bank account verification
- **Sales History**:
  - ALL successful sales: +5 points each

### Seller Tiers
1. **Guest Sellers** (0 points)
   - Max 5 active listings
   - 5% platform fee
   - DasWos Coins only

2. **OpenSphere** (0-69 points)
   - 0-29: New sellers, limited features
   - 30-69: Verified account, more features

3. **SafeSphere** (70+ points)
   - Full verification benefits
   - Lower fees
   - All payment methods available

## 5. Verification Flows

### Account Creation
1. User provides required information
2. System verifies email
3. +30 trust points awarded
4. Additional verification options unlocked

### Identity Verification (KYC)
1. User initiates verification
2. Redirect to third-party KYC provider
3. On success:
   - +30 trust points
   - Tier updated if applicable
   - Additional features unlocked

## 6. Backend Implementation

### Required Endpoints
- `POST /api/listings` - Create new listing
- `GET /api/trust-score` - Get current trust score
- `POST /api/verify/identity` - Initiate KYC
- `GET /api/verify/status` - Check verification status

### Data Models
```typescript
interface User {
  id: string;
  pseudonymousId: string;
  email: string;
  fullName: string;
  businessName?: string;
  contactInfo: ContactInfo;
  address: Address;
  trustScore: number;
  verificationStatus: 'unverified' | 'pending' | 'verified';
  tier: 'guest' | 'opensphere' | 'safesphere';
  createdAt: Date;
  updatedAt: Date;
}

interface Listing {
  id: string;
  sellerId: string;
  title: string;
  description: string;
  price: number;
  category: string;
  status: 'draft' | 'active' | 'sold' | 'removed';
  trustScoreAtCreation: number;
  createdAt: Date;
  updatedAt: Date;
}
```

## 7. Privacy and Compliance

### Data Collection
- Minimal required data collection
- Clear consent for all data processing
- Secure storage of sensitive information

### User Rights
- Right to access data
- Right to erasure
- Data portability

### Third-Party Integrations
- KYC providers
- Payment processors
- Email/SMS services

## 8. Implementation Timeline

### Phase 1: Core Functionality (2 weeks)
- User registration and authentication
- Basic listing creation
- Trust score calculation
- Simple dashboard

### Phase 2: Verification (2 weeks)
- KYC integration
- Document upload and verification
- Trust score updates

### Phase 3: Marketplace Features (2 weeks)
- Search and discovery
- Purchase flow
- Review system

## 9. Testing Plan

### Unit Tests
- Trust score calculations
- Tier assignments
- Form validations

### Integration Tests
- User registration flow
- Listing creation
- Purchase process

### User Acceptance Testing
- Real user testing
- Feedback collection
- Performance testing

## 10. Deployment Strategy

### Staging Environment
- Full testing of all features
- Load testing
- Security audit

### Production Rollout
- Phased rollout
- Monitoring and metrics
- Quick rollback plan

## 11. Success Metrics
- Number of new seller signups
- Conversion rate from guest to registered
- Average trust score increase
- Listing quality metrics
- Transaction volume
