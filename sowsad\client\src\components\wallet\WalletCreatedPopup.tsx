import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, CheckCir<PERSON>, AlertTriangle, Wallet } from 'lucide-react';

interface WalletCreatedPopupProps {
  walletId: string;
  onClose: () => void;
  onCreatePassword: () => void;
}

export default function WalletCreatedPopup({ walletId, onClose, onCreatePassword }: WalletCreatedPopupProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(walletId);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy wallet ID:', err);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <Wallet className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">DasWos Wallet Created!</h2>
              <p className="text-sm text-gray-500">Your personal DasWos wallet is ready</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Success Message */}
          <div className="flex items-start space-x-3 mb-6">
            <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm text-gray-700">
                Congratulations! We've automatically created a DasWos wallet for your account.
              </p>
            </div>
          </div>

          {/* Wallet ID Display */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Your DasWos Wallet ID:
            </label>
            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-white border border-gray-300 rounded-md px-3 py-2 font-mono text-sm">
                {walletId}
              </div>
              <button
                onClick={handleCopy}
                className="flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                {copied ? (
                  <>
                    <CheckCircle className="w-4 h-4" />
                    <span className="text-sm">Copied!</span>
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    <span className="text-sm">Copy</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Important Notice */}
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="text-sm font-medium text-amber-800 mb-1">Important!</h3>
                <ul className="text-sm text-amber-700 space-y-1">
                  <li>• Save this Wallet ID - you'll need it to access your DasWos Coins</li>
                  <li>• Create a password for your wallet to start using it</li>
                  <li>• Your DasWos Coins balance is linked to this wallet</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Next Steps */}
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-gray-900">Next Steps:</h3>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">1</div>
                <span>Save your Wallet ID (copy it to a safe place)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">2</div>
                <span>Create a password for your wallet</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-600">3</div>
                <span>Start using your DasWos Coins!</span>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50 rounded-b-lg">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            I'll do this later
          </button>
          <button
            onClick={onCreatePassword}
            className="px-6 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            Create Password Now
          </button>
        </div>
      </div>
    </div>
  );
}
