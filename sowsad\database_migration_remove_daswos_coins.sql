-- =====================================================
-- MIGRATION: Remove DasWos Coins from Main App Database
-- =====================================================
-- This migration removes DasWos coins functionality from the main daswos-18 database
-- DasWos coins are now managed exclusively via the external wallet system
-- 
-- IMPORTANT: Run this on your main daswos-18 database, NOT the wallet database
-- =====================================================

-- Start transaction to ensure atomicity
BEGIN;

-- Step 1: Backup existing data (optional - uncomment if you want to keep a backup)
-- CREATE TABLE daswos_coins_transactions_backup AS SELECT * FROM daswos_coins_transactions;
-- CREATE TABLE users_backup AS SELECT id, username, daswos_coins_balance FROM users WHERE daswos_coins_balance > 0;

-- Step 2: Drop foreign key constraints that reference daswos_coins_transactions
-- (Check if these constraints exist first)
DO $$ 
BEGIN
    -- Drop constraint if it exists
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_daswos_coins_user' 
        AND table_name = 'daswos_coins_transactions'
    ) THEN
        ALTER TABLE daswos_coins_transactions DROP CONSTRAINT fk_daswos_coins_user;
    END IF;
END $$;

-- Step 3: Drop indexes related to daswos_coins_transactions
DROP INDEX IF EXISTS idx_daswos_coins_user;
DROP INDEX IF EXISTS idx_daswos_coins_type;
DROP INDEX IF EXISTS idx_daswos_coins_created_at;

-- Step 4: Drop the daswos_coins_transactions table
DROP TABLE IF EXISTS daswos_coins_transactions CASCADE;

-- Step 5: Remove daswos_coins_balance column from users table
-- First, let's check if the column exists
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'daswos_coins_balance'
    ) THEN
        -- Remove the column
        ALTER TABLE users DROP COLUMN daswos_coins_balance;
        
        -- Log the change
        RAISE NOTICE 'Removed daswos_coins_balance column from users table';
    ELSE
        RAISE NOTICE 'daswos_coins_balance column does not exist in users table';
    END IF;
END $$;

-- Step 6: Add a comment to the users table to document the change
COMMENT ON TABLE users IS 'User accounts for daswos-18. DasWos coins are managed via external wallet system.';

-- Step 7: Update any existing triggers or functions that might reference the removed columns
-- (This is a safety measure - adjust based on your specific database setup)

-- Step 8: Create a migration log entry (optional)
DO $$
BEGIN
    -- Create migration log table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'migration_log') THEN
        CREATE TABLE migration_log (
            id SERIAL PRIMARY KEY,
            migration_name TEXT NOT NULL,
            executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            description TEXT
        );
    END IF;
    
    -- Log this migration
    INSERT INTO migration_log (migration_name, description) VALUES 
    (
        'remove_daswos_coins_from_main_db', 
        'Removed DasWos coins functionality from main database. Coins now managed via external wallet system.'
    );
END $$;

-- Step 9: Verify the changes
DO $$
DECLARE
    table_exists BOOLEAN;
    column_exists BOOLEAN;
BEGIN
    -- Check if daswos_coins_transactions table was removed
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables 
        WHERE table_name = 'daswos_coins_transactions'
    ) INTO table_exists;
    
    -- Check if daswos_coins_balance column was removed
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' 
        AND column_name = 'daswos_coins_balance'
    ) INTO column_exists;
    
    -- Report results
    IF NOT table_exists THEN
        RAISE NOTICE '✅ SUCCESS: daswos_coins_transactions table removed';
    ELSE
        RAISE WARNING '❌ WARNING: daswos_coins_transactions table still exists';
    END IF;
    
    IF NOT column_exists THEN
        RAISE NOTICE '✅ SUCCESS: daswos_coins_balance column removed from users table';
    ELSE
        RAISE WARNING '❌ WARNING: daswos_coins_balance column still exists in users table';
    END IF;
    
    RAISE NOTICE '🎉 Migration completed successfully!';
    RAISE NOTICE '📝 DasWos coins are now managed exclusively via the external wallet system';
    RAISE NOTICE '🔗 Users must connect a wallet to access DasWos coins';
END $$;

-- Commit the transaction
COMMIT;

-- =====================================================
-- POST-MIGRATION NOTES:
-- =====================================================
-- 1. Users will no longer have individual DasWos coins balances
-- 2. All DasWos coins functionality is now via external wallet system
-- 3. Users must connect a wallet (like demo-wallet) to access coins
-- 4. Wallet connections are tracked for accountability
-- 5. Update your application code to remove references to user DasWos coins
-- =====================================================

-- Optional: Display current table structure to verify changes
SELECT 
    'users' as table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
ORDER BY ordinal_position;
