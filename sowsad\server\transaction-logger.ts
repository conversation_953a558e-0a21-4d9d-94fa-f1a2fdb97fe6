import fs from 'fs';
import path from 'path';

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const logFilePath = path.join(logsDir, 'transactions.log');

// Ensure log file exists
if (!fs.existsSync(logFilePath)) {
  fs.writeFileSync(logFilePath, '=== DasWos Transaction Log Started ===\n');
}

interface LogEntry {
  timestamp: string;
  type: 'USER_LOGIN' | 'USER_LOGOUT' | 'WALLET_CONNECT' | 'WALLET_DISCONNECT' | 'TRANSACTION_START' | 'TRANSACTION_SUCCESS' | 'TRANSACTION_FAILED' | 'COINS_ADDED' | 'PAYMENT_PROCESSED';
  userId?: number;
  username?: string;
  walletId?: string;
  amount?: number;
  transactionId?: string;
  balance?: number;
  details?: string;
  error?: string;
}

function formatLogEntry(entry: LogEntry): string {
  const timestamp = entry.timestamp;
  const type = entry.type.padEnd(18);
  
  let message = '';
  
  switch (entry.type) {
    case 'USER_LOGIN':
      message = `User "${entry.username}" (ID: ${entry.userId}) logged in`;
      break;
    case 'USER_LOGOUT':
      message = `User "${entry.username}" (ID: ${entry.userId}) logged out`;
      break;
    case 'WALLET_CONNECT':
      message = `Wallet "${entry.walletId}" connected for user "${entry.username}" (ID: ${entry.userId})`;
      break;
    case 'WALLET_DISCONNECT':
      message = `Wallet "${entry.walletId}" disconnected for user "${entry.username}" (ID: ${entry.userId})`;
      break;
    case 'TRANSACTION_START':
      message = `[${entry.transactionId}] User "${entry.username}" (ID: ${entry.userId}) started transaction for ${entry.amount} DasWos Coins via wallet "${entry.walletId}"`;
      break;
    case 'PAYMENT_PROCESSED':
      message = `[${entry.transactionId}] Payment of $${entry.amount} processed successfully for user "${entry.username}" (ID: ${entry.userId})`;
      break;
    case 'COINS_ADDED':
      message = `[${entry.transactionId}] ${entry.amount} DasWos Coins added to user "${entry.username}" (ID: ${entry.userId}). New balance: ${entry.balance}`;
      break;
    case 'TRANSACTION_SUCCESS':
      message = `[${entry.transactionId}] Transaction completed successfully: User "${entry.username}" (ID: ${entry.userId}) purchased ${entry.amount} DasWos Coins. Final balance: ${entry.balance}`;
      break;
    case 'TRANSACTION_FAILED':
      message = `[${entry.transactionId}] Transaction failed for user "${entry.username}" (ID: ${entry.userId}): ${entry.error}`;
      break;
    default:
      message = entry.details || 'Unknown event';
  }
  
  return `${timestamp} | ${type} | ${message}\n`;
}

export function logTransaction(entry: LogEntry) {
  try {
    // Add timestamp if not provided
    if (!entry.timestamp) {
      entry.timestamp = new Date().toISOString();
    }
    
    const logLine = formatLogEntry(entry);
    
    // Write to file (append)
    fs.appendFileSync(logFilePath, logLine);
    
    // Also log to console with emoji for visibility
    const emoji = getEmojiForType(entry.type);
    console.log(`${emoji} ${logLine.trim()}`);
    
  } catch (error) {
    console.error('Failed to write to transaction log:', error);
  }
}

function getEmojiForType(type: LogEntry['type']): string {
  switch (type) {
    case 'USER_LOGIN': return '🔐';
    case 'USER_LOGOUT': return '🔓';
    case 'WALLET_CONNECT': return '🔗';
    case 'WALLET_DISCONNECT': return '🔌';
    case 'TRANSACTION_START': return '🔄';
    case 'PAYMENT_PROCESSED': return '💳';
    case 'COINS_ADDED': return '💰';
    case 'TRANSACTION_SUCCESS': return '✅';
    case 'TRANSACTION_FAILED': return '❌';
    default: return '📝';
  }
}

// Helper functions for common log entries
export const TransactionLogger = {
  userLogin: (userId: number, username: string) => {
    logTransaction({
      type: 'USER_LOGIN',
      userId,
      username,
      timestamp: new Date().toISOString()
    });
  },
  
  userLogout: (userId: number, username: string) => {
    logTransaction({
      type: 'USER_LOGOUT',
      userId,
      username,
      timestamp: new Date().toISOString()
    });
  },
  
  walletConnect: (userId: number, username: string, walletId: string) => {
    logTransaction({
      type: 'WALLET_CONNECT',
      userId,
      username,
      walletId,
      timestamp: new Date().toISOString()
    });
  },
  
  walletDisconnect: (userId: number, username: string, walletId: string) => {
    logTransaction({
      type: 'WALLET_DISCONNECT',
      userId,
      username,
      walletId,
      timestamp: new Date().toISOString()
    });
  },
  
  transactionStart: (transactionId: string, userId: number, username: string, walletId: string, amount: number) => {
    logTransaction({
      type: 'TRANSACTION_START',
      transactionId,
      userId,
      username,
      walletId,
      amount,
      timestamp: new Date().toISOString()
    });
  },
  
  paymentProcessed: (transactionId: string, userId: number, username: string, amount: number) => {
    logTransaction({
      type: 'PAYMENT_PROCESSED',
      transactionId,
      userId,
      username,
      amount,
      timestamp: new Date().toISOString()
    });
  },
  
  coinsAdded: (transactionId: string, userId: number, username: string, amount: number, newBalance: number) => {
    logTransaction({
      type: 'COINS_ADDED',
      transactionId,
      userId,
      username,
      amount,
      balance: newBalance,
      timestamp: new Date().toISOString()
    });
  },
  
  transactionSuccess: (transactionId: string, userId: number, username: string, amount: number, finalBalance: number) => {
    logTransaction({
      type: 'TRANSACTION_SUCCESS',
      transactionId,
      userId,
      username,
      amount,
      balance: finalBalance,
      timestamp: new Date().toISOString()
    });
  },
  
  transactionFailed: (transactionId: string, userId: number, username: string, error: string) => {
    logTransaction({
      type: 'TRANSACTION_FAILED',
      transactionId,
      userId,
      username,
      error,
      timestamp: new Date().toISOString()
    });
  }
};

// Function to read recent log entries
export function getRecentTransactions(lines: number = 50): string[] {
  try {
    if (!fs.existsSync(logFilePath)) {
      return [];
    }
    
    const content = fs.readFileSync(logFilePath, 'utf-8');
    const allLines = content.split('\n').filter(line => line.trim());
    
    // Return the last N lines
    return allLines.slice(-lines);
  } catch (error) {
    console.error('Failed to read transaction log:', error);
    return [];
  }
}

// Function to clear old logs (keep last 1000 entries)
export function cleanupOldLogs() {
  try {
    const recentLines = getRecentTransactions(1000);
    const header = '=== DasWos Transaction Log ===\n';
    const newContent = header + recentLines.join('\n') + '\n';
    
    fs.writeFileSync(logFilePath, newContent);
    console.log('📁 Transaction log cleaned up, kept last 1000 entries');
  } catch (error) {
    console.error('Failed to cleanup transaction log:', error);
  }
}

console.log(`📝 Transaction logger initialized. Log file: ${logFilePath}`);
