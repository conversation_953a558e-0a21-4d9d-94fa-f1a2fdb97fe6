import React, { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Volume2 } from 'lucide-react';
import { useVoiceRecognition } from '@/hooks/use-voice-recognition';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface VoiceControlProps {
  isAiModeEnabled: boolean;
  onSearchCommand: (query: string) => void;
  onAutoShopCommand: () => void;
  onPurchaseCommand?: (quantity: number) => void;
  className?: string;
}

const VoiceControl: React.FC<VoiceControlProps> = ({
  isAiModeEnabled,
  onSearchCommand,
  onAutoShopCommand,
  onPurchaseCommand,
  className
}) => {
  const {
    isListening,
    isSupported,
    transcript,
    interimTranscript,
    error,
    startListening,
    stopListening,
    processCommand
  } = useVoiceRecognition({
    continuous: false,
    interimResults: true,
    language: 'en-US'
  });

  // Handle voice commands
  useEffect(() => {
    if (transcript) {
      const result = processCommand(transcript, 1.0);

      if (result) {
        switch (result.type) {
          case 'search':
            if (result.query) {
              onSearchCommand(result.query);
            }
            break;
          case 'autoshop':
            onAutoShopCommand();
            break;
          case 'purchase':
            if (onPurchaseCommand && result.quantity) {
              onPurchaseCommand(result.quantity);
            }
            break;
          case 'navigation':
            // Navigation is handled within the hook
            break;
        }
      }
    }
  }, [transcript, processCommand, onSearchCommand, onAutoShopCommand, onPurchaseCommand]);

  // Basic voice control - works regardless of AI mode

  // Show disabled state if not supported
  if (!isSupported) {
    console.log('VoiceControl showing disabled state - not supported');
    return (
      <div className={cn("relative", className)}>
        <Button
          type="button"
          variant="ghost"
          size="icon"
          disabled={true}
          className="relative overflow-hidden opacity-50"
          aria-label="Voice recognition not supported"
          title="Voice recognition not supported in this browser"
        >
          <MicOff className="h-4 w-4 text-gray-400" />
        </Button>
      </div>
    );
  }

  const handleToggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  return (
    <div className={cn("relative", className)}>
      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={handleToggleListening}
        className={cn(
          "relative overflow-hidden group transition-all duration-300",
          isListening
            ? "bg-gradient-to-r from-red-500/20 via-orange-500/20 to-yellow-500/20 text-red-400 hover:text-red-300"
            : "bg-gradient-to-r from-blue-500/20 via-green-500/20 to-yellow-400/20 text-blue-400 hover:text-blue-300"
        )}
        disabled={!isSupported}
        aria-label={isListening ? "Stop voice recognition" : "Start voice recognition"}
      >
        {/* Animated background for listening state */}
        {isListening && (
          <>
            {/* Pulsing background */}
            <div className="absolute inset-0 bg-gradient-to-r from-red-400/30 via-orange-400/30 to-yellow-400/30 animate-pulse"></div>

            {/* Sound wave effect */}
            <div className="absolute inset-0 opacity-40">
              <div className="absolute top-2 left-2 w-1 h-6 bg-red-300 rounded-full animate-pulse" style={{ animationDelay: '0s' }}></div>
              <div className="absolute top-1 left-4 w-1 h-8 bg-orange-300 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <div className="absolute top-3 left-6 w-1 h-4 bg-yellow-300 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
            </div>
          </>
        )}

        {/* Microphone icon */}
        <div className="relative z-10">
          {isListening ? (
            <MicOff className="h-4 w-4 animate-pulse" />
          ) : (
            <Mic className="h-4 w-4" />
          )}
        </div>

        {/* Listening indicator */}
        {isListening && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"></div>
        )}
      </Button>

      {/* Voice feedback display */}
      {(isListening || interimTranscript || transcript) && (
        <div className="absolute top-full left-0 mt-2 p-3 bg-gray-900/95 backdrop-blur-sm border border-gray-600/50 rounded-lg shadow-lg min-w-[200px] max-w-[300px] z-50">
          {/* Status indicator */}
          <div className="flex items-center gap-2 mb-2">
            <Volume2 className="h-3 w-3 text-blue-400" />
            <span className="text-xs font-mono text-blue-300">
              {isListening ? 'LISTENING...' : 'PROCESSING...'}
            </span>
          </div>

          {/* Transcript display */}
          {(interimTranscript || transcript) && (
            <div className="space-y-1">
              {interimTranscript && (
                <p className="text-xs text-gray-400 font-mono italic">
                  {interimTranscript}
                </p>
              )}
              {transcript && (
                <p className="text-xs text-white font-mono">
                  "{transcript}"
                </p>
              )}
            </div>
          )}

          {/* Error display */}
          {error && (
            <p className="text-xs text-red-400 font-mono mt-2">
              Error: {error}
            </p>
          )}

          {/* Voice commands help */}
          {isListening && !interimTranscript && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <p className="text-xs text-gray-500 font-mono mb-1">Try saying:</p>
              <ul className="text-xs text-gray-400 font-mono space-y-0.5">
                <li>• "Search for shoes"</li>
                <li>• "Go to my profile"</li>
                <li>• "Start AutoShop"</li>
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default VoiceControl;
