import { useState, useRef, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';

interface AIResponse {
  intent: string;
  action?: string;
  parameters?: any;
  response: string;
  confidence: number;
}

interface VoiceOptions {
  enableTextToSpeech?: boolean;
}

export const useWhisperVoice = (options: VoiceOptions = {}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSupported, setIsSupported] = useState(true);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [transcript, setTranscript] = useState('');
  const [aiResponse, setAiResponse] = useState<AIResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [conversationHistory, setConversationHistory] = useState<any[]>([]);
  const [isWakeWordListening, setIsWakeWordListening] = useState(false);

  // Use Web Speech API instead of MediaRecorder for better compatibility
  const recognitionRef = useRef<any>(null);
  const wakeWordRecognitionRef = useRef<any>(null);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Wake word settings
  const wakeWord = options.wakeWord?.toLowerCase() || 'daswos';
  const enableWakeWord = options.enableWakeWord || false;

  // Check if Web Speech API is supported on mount
  useEffect(() => {
    const checkSupport = () => {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

      if (!SpeechRecognition) {
        console.log('❌ Web Speech API not supported in this browser');
        setIsSupported(false);
        setError('Speech recognition not supported in this browser');
      } else {
        console.log('✅ Web Speech API is supported');
        setIsSupported(true);
      }
    };

    checkSupport();
  }, []);

  // Check microphone permissions (simplified for Web Speech API)
  const checkMicrophonePermission = useCallback(async () => {
    console.log('🎤 Checking microphone permissions for Web Speech API...');

    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        console.log('❌ MediaDevices API not supported');
        setHasPermission(false);
        setError('Microphone not supported in this browser');
        return false;
      }

      console.log('✅ MediaDevices API is available');

      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        console.log('✅ Microphone access granted!');
        setHasPermission(true);
        setError(null);

        // Stop the stream immediately as we only needed to check permission
        stream.getTracks().forEach(track => {
          console.log('🛑 Stopping track:', track.label);
          track.stop();
        });

        toast({
          title: 'Microphone Access Granted',
          description: 'You can now use voice commands!',
        });

        return true;
      } catch (mediaError: any) {
        console.error('❌ Microphone access denied:', mediaError);
        setHasPermission(false);

        let errorMessage = 'Microphone access denied.';
        if (mediaError.name === 'NotAllowedError') {
          errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings.';
        } else if (mediaError.name === 'NotFoundError') {
          errorMessage = 'No microphone found. Please connect a microphone and try again.';
        } else if (mediaError.name === 'NotReadableError') {
          errorMessage = 'Microphone is being used by another application.';
        }

        setError(errorMessage);
        toast({
          title: 'Microphone Access Required',
          description: errorMessage,
          variant: 'destructive',
        });
        return false;
      }
    } catch (error) {
      console.error('💥 Error checking microphone permission:', error);
      setHasPermission(false);
      setError('Failed to check microphone permissions');
      return false;
    }
  }, [toast]);

  // Start recording using Web Speech API
  const startRecording = useCallback(async () => {
    console.log('🎯 startRecording called');

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      console.log('❌ Web Speech API not supported');
      setError('Speech recognition not supported in this browser');
      return;
    }

    console.log('✅ Web Speech API is available, starting recognition...');

    try {
      const recognition = new SpeechRecognition();
      recognitionRef.current = recognition;

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      let finalTranscript = '';
      let hasDetectedSpeech = false;

      recognition.onstart = () => {
        console.log('🎤 Speech recognition started');
        setIsRecording(true);
        setError(null);
        setTranscript('');
        setAiResponse(null);

        // Emit voice status event
        const statusEvent = new CustomEvent('voiceStatus', {
          detail: { status: 'listening', message: '🎤 Listening... Speak now' }
        });
        window.dispatchEvent(statusEvent);

        toast({
          title: 'DasWos AI Listening',
          description: 'Speak your command now...',
        });
      };

      recognition.onresult = (event: any) => {
        console.log('📝 Speech recognition result:', event);
        let interim = '';
        let final = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            final += transcript;
          } else {
            interim += transcript;
          }
        }

        if (interim) {
          setTranscript(`Hearing: "${interim}"`);
          hasDetectedSpeech = true;
        }

        if (final) {
          finalTranscript = final;
          setTranscript(`You said: "${final}"`);
          console.log('✅ Final transcript:', final);

          // Emit processing status
          const statusEvent = new CustomEvent('voiceStatus', {
            detail: { status: 'processing', message: '🤖 Processing your request...' }
          });
          window.dispatchEvent(statusEvent);

          // Process with AI
          processWithAI(final);
          recognition.stop();
        }
      };

      recognition.onerror = (event: any) => {
        console.error('❌ Speech recognition error:', event.error);
        setError(`Speech recognition error: ${event.error}`);
        setIsRecording(false);

        toast({
          title: 'Speech Recognition Error',
          description: `Error: ${event.error}`,
          variant: 'destructive',
        });
      };

      recognition.onend = () => {
        console.log('🛑 Speech recognition ended');
        if (!hasDetectedSpeech && !finalTranscript) {
          setTranscript('No speech detected. Try speaking louder or closer to the microphone.');
          // Emit idle status
          const statusEvent = new CustomEvent('voiceStatus', {
            detail: { status: 'idle', message: '' }
          });
          window.dispatchEvent(statusEvent);
        }
        setIsRecording(false);
      };

      recognition.onsoundstart = () => {
        console.log('🔊 Sound detected');
        setTranscript('Sound detected! Keep speaking...');
      };

      recognition.onspeechstart = () => {
        console.log('🗣️ Speech detected');
        setTranscript('Speech detected! Continue speaking...');
        hasDetectedSpeech = true;
      };

      recognition.start();

      // Auto-stop after 30 seconds
      setTimeout(() => {
        if (recognition && recognitionRef.current === recognition) {
          recognition.stop();
        }
      }, 30000);

    } catch (err: any) {
      console.error('❌ Failed to start speech recognition:', err);
      setError('Failed to start speech recognition');
      setIsRecording(false);

      toast({
        title: 'Speech Recognition Error',
        description: 'Failed to start speech recognition. Please try again.',
        variant: 'destructive',
      });
    }
  }, [toast]);

  // Stop recording
  const stopRecording = useCallback(() => {
    console.log('🛑 stopRecording called');

    if (recognitionRef.current) {
      recognitionRef.current.stop();
      console.log('🛑 Speech recognition stopped');
    }

    setIsRecording(false);

    // Emit idle status
    const statusEvent = new CustomEvent('voiceStatus', {
      detail: { status: 'idle', message: '' }
    });
    window.dispatchEvent(statusEvent);
  }, []);

  // Process voice command with local pattern matching (no server needed)
  const processVoiceCommand = useCallback(async (userQuery: string) => {
    console.log('🤖 Processing voice command locally:', userQuery);
    setIsProcessing(true);

    try {
      // Clean up the query by removing the wake word
      const cleanQuery = userQuery.toLowerCase()
        .replace(/^(daswos|das wos|das was|das boss|das voice)\s*/i, '')
        .trim();

      console.log('🧹 Cleaned query:', cleanQuery);

      // Local pattern matching for commands
      const aiResponse = processWithLocalPatterns(cleanQuery);

      console.log('🔍 Local AI response:', aiResponse);
      setAiResponse(aiResponse);

      // Dispatch voice command result for other components to handle
      const voiceResultEvent = new CustomEvent('voiceCommandResult', {
        detail: {
          userQuery: userQuery,
          aiResponse: aiResponse,
          audio: null
        }
      });
      window.dispatchEvent(voiceResultEvent);
      console.log('📡 Dispatched voiceCommandResult event:', { userQuery, aiResponse });

      // Handle actions
      if (aiResponse) {
        handleVoiceAction(aiResponse);
      }

      // Simple text-to-speech using browser API if enabled
      if (options.enableTextToSpeech && aiResponse.response) {
        try {
          console.log('🔊 Speaking response...');

          // Emit speaking status
          const speakingEvent = new CustomEvent('voiceStatus', {
            detail: { status: 'speaking', message: '🗣️ DasWos AI is responding...' }
          });
          window.dispatchEvent(speakingEvent);

          // Use browser's speech synthesis
          const utterance = new SpeechSynthesisUtterance(aiResponse.response);
          utterance.rate = 0.9;
          utterance.pitch = 1.0;
          utterance.volume = 0.8;

          utterance.onend = () => {
            const idleEvent = new CustomEvent('voiceStatus', {
              detail: { status: 'idle', message: '' }
            });
            window.dispatchEvent(idleEvent);
          };

          speechSynthesis.speak(utterance);
          console.log('🔊 Speech synthesis started');
        } catch (speechError) {
          console.error('❌ Failed to speak response:', speechError);
          // Emit idle status on error
          const idleEvent = new CustomEvent('voiceStatus', {
            detail: { status: 'idle', message: '' }
          });
          window.dispatchEvent(idleEvent);
        }
      } else {
        // No TTS, emit idle status immediately
        const idleEvent = new CustomEvent('voiceStatus', {
          detail: { status: 'idle', message: '' }
        });
        window.dispatchEvent(idleEvent);
      }

      toast({
        title: 'DasWos AI Voice Command',
        description: `"${cleanQuery}" → ${aiResponse.response}`,
      });

    } catch (error) {
      console.error('❌ Voice processing error:', error);
      setError(`Processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);

      toast({
        title: 'Processing Error',
        description: 'Failed to process voice command. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  }, [options.enableTextToSpeech, toast]);

  // Handle AI actions
  const handleAIAction = useCallback((response: AIResponse) => {
    console.log('🎯 Handling AI action:', response);

    if (response.intent === 'search' && response.parameters?.query) {
      console.log('🔍 Performing search:', response.parameters.query);
      // Trigger search
      const searchEvent = new CustomEvent('aiSearch', {
        detail: { query: response.parameters.query }
      });
      window.dispatchEvent(searchEvent);
    } else if (response.intent === 'navigation' && response.parameters?.route) {
      console.log('🧭 Whisper Voice: Navigating to:', response.parameters.route);
      console.log('🧭 Current location before navigation:', window.location.pathname);
      setLocation(response.parameters.route);
      console.log('🧭 setLocation called with:', response.parameters.route);
    } else if (response.intent === 'autoshop') {
      console.log('🛒 Starting AutoShop');
      const autoshopEvent = new CustomEvent('aiAutoshop');
      window.dispatchEvent(autoshopEvent);
    } else if (response.intent === 'purchase') {
      console.log('🛒 Purchase command:', response.parameters?.quantity);
      const purchaseEvent = new CustomEvent('aiPurchase', {
        detail: { quantity: response.parameters?.quantity || 1 }
      });
      window.dispatchEvent(purchaseEvent);
    }
  }, [setLocation]);

  // Start wake word listening
  const startWakeWordListening = useCallback(async () => {
    console.log('👂 Starting wake word listening for:', wakeWord);

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      console.log('❌ Web Speech API not supported for wake word');
      return;
    }

    try {
      const recognition = new SpeechRecognition();
      wakeWordRecognitionRef.current = recognition;

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      recognition.onstart = () => {
        console.log('👂 Wake word listening started');
        setIsWakeWordListening(true);
      };

      recognition.onresult = (event: any) => {
        let interimTranscript = '';
        let finalTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript.toLowerCase();
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        // Check for wake word in both interim and final results
        const fullTranscript = (finalTranscript + ' ' + interimTranscript).toLowerCase();

        if (fullTranscript.includes(wakeWord)) {
          console.log('🎯 Wake word detected:', wakeWord);

          // Stop wake word listening
          recognition.stop();

          // Start main recording
          setTimeout(() => {
            startRecording();
          }, 500); // Small delay to ensure clean transition

          toast({
            title: 'DasWos Activated!',
            description: 'Wake word detected. Listening for your command...',
          });
        }
      };

      recognition.onerror = (event: any) => {
        console.error('❌ Wake word recognition error:', event.error);
        if (event.error !== 'no-speech') {
          // Restart wake word listening after error (except for no-speech)
          setTimeout(() => {
            if (enableWakeWord && !isRecording) {
              startWakeWordListening();
            }
          }, 1000);
        }
      };

      recognition.onend = () => {
        console.log('👂 Wake word listening ended');
        setIsWakeWordListening(false);

        // Restart wake word listening if it should be active
        if (enableWakeWord && !isRecording) {
          setTimeout(() => {
            startWakeWordListening();
          }, 1000);
        }
      };

      recognition.start();

    } catch (err: any) {
      console.error('❌ Failed to start wake word listening:', err);
      setIsWakeWordListening(false);
    }
  }, [wakeWord, enableWakeWord, isRecording, startRecording, toast]);

  // Stop wake word listening
  const stopWakeWordListening = useCallback(() => {
    console.log('🛑 Stopping wake word listening');

    if (wakeWordRecognitionRef.current) {
      wakeWordRecognitionRef.current.stop();
    }

    setIsWakeWordListening(false);
  }, []);

  // Start wake word listening when enabled
  useEffect(() => {
    if (enableWakeWord && hasPermission && !isRecording && !isWakeWordListening) {
      console.log('🚀 Auto-starting wake word listening');
      startWakeWordListening();
    } else if (!enableWakeWord && isWakeWordListening) {
      console.log('🛑 Stopping wake word listening (disabled)');
      stopWakeWordListening();
    }

    return () => {
      if (wakeWordRecognitionRef.current) {
        wakeWordRecognitionRef.current.stop();
      }
    };
  }, [enableWakeWord, hasPermission, isRecording, isWakeWordListening, startWakeWordListening, stopWakeWordListening]);

  return {
    isRecording,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    aiResponse,
    error,
    isWakeWordListening,
    startRecording,
    stopRecording,
    checkMicrophonePermission,
    startWakeWordListening,
    stopWakeWordListening
  };
};
