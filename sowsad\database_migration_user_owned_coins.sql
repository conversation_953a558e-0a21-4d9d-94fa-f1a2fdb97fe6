-- =====================================================
-- MIGRATION: Implement User-Owned DasWos Coins System
-- =====================================================
-- This migration implements the new architecture where:
-- 1. Users own DasWos coins balance (stored in user account)
-- 2. Wallets are just interfaces for accessing user balance
-- 3. Both user auth + wallet connection required for full functionality
-- =====================================================

-- Start transaction to ensure atomicity
BEGIN;

-- Step 1: Add daswos_coins_balance column to users table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'daswos_coins_balance'
    ) THEN
        ALTER TABLE users ADD COLUMN daswos_coins_balance INTEGER DEFAULT 0 NOT NULL;
        RAISE NOTICE '✅ Added daswos_coins_balance column to users table';
    ELSE
        RAISE NOTICE 'ℹ️  daswos_coins_balance column already exists in users table';
    END IF;
END $$;

-- Step 1b: Add wallet_id column to users table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'wallet_id'
    ) THEN
        ALTER TABLE users ADD COLUMN wallet_id TEXT;
        RAISE NOTICE '✅ Added wallet_id column to users table';
    ELSE
        RAISE NOTICE 'ℹ️  wallet_id column already exists in users table';
    END IF;
END $$;

-- Step 2: Create daswos_coins_transactions table (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'daswos_coins_transactions'
    ) THEN
        CREATE TABLE daswos_coins_transactions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL,
            amount INTEGER NOT NULL, -- Positive for add, negative for spend
            transaction_type TEXT NOT NULL, -- "purchase", "spend", "refund", "bonus"
            description TEXT NOT NULL,
            status TEXT NOT NULL DEFAULT 'completed',
            metadata JSONB DEFAULT '{}',
            wallet_id TEXT, -- Which wallet was used for this transaction (for audit)
            related_order_id INTEGER,
            related_split_buy_id INTEGER,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            completed_at TIMESTAMP WITH TIME ZONE
        );
        RAISE NOTICE '✅ Created daswos_coins_transactions table';
    ELSE
        RAISE NOTICE 'ℹ️  daswos_coins_transactions table already exists';
    END IF;
END $$;

-- Step 3: Add wallet_id column to existing transactions table (if not exists)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'daswos_coins_transactions'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'daswos_coins_transactions'
        AND column_name = 'wallet_id'
    ) THEN
        ALTER TABLE daswos_coins_transactions ADD COLUMN wallet_id TEXT;
        RAISE NOTICE '✅ Added wallet_id column to daswos_coins_transactions table';
    ELSE
        RAISE NOTICE 'ℹ️  wallet_id column already exists or table does not exist';
    END IF;
END $$;

-- Step 4: Add foreign key constraint for daswos_coins_transactions (if not exists)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_daswos_coins_user'
        AND table_name = 'daswos_coins_transactions'
    ) THEN
        ALTER TABLE daswos_coins_transactions
        ADD CONSTRAINT fk_daswos_coins_user
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
        RAISE NOTICE '✅ Added foreign key constraint for daswos_coins_transactions';
    ELSE
        RAISE NOTICE 'ℹ️  Foreign key constraint already exists';
    END IF;
END $$;

-- Step 5: Create indexes for performance (if not exist)
DO $$
BEGIN
    -- Index on user_id
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'idx_daswos_coins_user'
    ) THEN
        CREATE INDEX idx_daswos_coins_user ON daswos_coins_transactions(user_id);
        RAISE NOTICE '✅ Created index on user_id';
    END IF;

    -- Index on transaction_type
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'idx_daswos_coins_type'
    ) THEN
        CREATE INDEX idx_daswos_coins_type ON daswos_coins_transactions(transaction_type);
        RAISE NOTICE '✅ Created index on transaction_type';
    END IF;

    -- Index on created_at
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'idx_daswos_coins_created'
    ) THEN
        CREATE INDEX idx_daswos_coins_created ON daswos_coins_transactions(created_at);
        RAISE NOTICE '✅ Created index on created_at';
    END IF;
END $$;

-- Step 6: Update column name if needed (type -> transaction_type)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'daswos_coins_transactions'
        AND column_name = 'type'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'daswos_coins_transactions'
        AND column_name = 'transaction_type'
    ) THEN
        ALTER TABLE daswos_coins_transactions RENAME COLUMN type TO transaction_type;
        RAISE NOTICE '✅ Renamed type column to transaction_type';
    ELSE
        RAISE NOTICE 'ℹ️  Column naming is already correct';
    END IF;
END $$;

-- Step 7: Add sample data for testing (optional)
DO $$
BEGIN
    -- Add some test coins to admin user if exists
    IF EXISTS (SELECT 1 FROM users WHERE username = 'admin') THEN
        UPDATE users SET daswos_coins_balance = 1000 WHERE username = 'admin';

        -- Add a sample transaction
        INSERT INTO daswos_coins_transactions (
            user_id,
            amount,
            transaction_type,
            description,
            metadata
        )
        SELECT
            id,
            1000,
            'admin',
            'Initial admin coins for testing',
            '{"source": "migration", "type": "initial_setup"}'::jsonb
        FROM users
        WHERE username = 'admin'
        AND NOT EXISTS (
            SELECT 1 FROM daswos_coins_transactions
            WHERE user_id = (SELECT id FROM users WHERE username = 'admin')
        );

        RAISE NOTICE '✅ Added test coins to admin user';
    END IF;
END $$;

-- Step 8: Create migration log entry
DO $$
BEGIN
    -- Create migration log table if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'migration_log') THEN
        CREATE TABLE migration_log (
            id SERIAL PRIMARY KEY,
            migration_name TEXT NOT NULL,
            executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            description TEXT
        );
    END IF;

    -- Log this migration
    INSERT INTO migration_log (migration_name, description) VALUES
    (
        'user_owned_daswos_coins_system',
        'Implemented user-owned DasWos coins system where balance is stored in user account and wallets serve as interface'
    );
END $$;

-- Step 9: Verify the migration
DO $$
DECLARE
    users_column_exists BOOLEAN;
    transactions_table_exists BOOLEAN;
    wallet_id_column_exists BOOLEAN;
    constraint_exists BOOLEAN;
BEGIN
    -- Check if users table has daswos_coins_balance column
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'users'
        AND column_name = 'daswos_coins_balance'
    ) INTO users_column_exists;

    -- Check if transactions table exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_name = 'daswos_coins_transactions'
    ) INTO transactions_table_exists;

    -- Check if wallet_id column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'daswos_coins_transactions'
        AND column_name = 'wallet_id'
    ) INTO wallet_id_column_exists;

    -- Check if foreign key constraint exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.table_constraints
        WHERE constraint_name = 'fk_daswos_coins_user'
    ) INTO constraint_exists;

    -- Report results
    IF users_column_exists THEN
        RAISE NOTICE '✅ SUCCESS: daswos_coins_balance column exists in users table';
    ELSE
        RAISE WARNING '❌ WARNING: daswos_coins_balance column missing from users table';
    END IF;

    IF transactions_table_exists THEN
        RAISE NOTICE '✅ SUCCESS: daswos_coins_transactions table exists';
    ELSE
        RAISE WARNING '❌ WARNING: daswos_coins_transactions table missing';
    END IF;

    IF wallet_id_column_exists THEN
        RAISE NOTICE '✅ SUCCESS: wallet_id column exists in transactions table';
    ELSE
        RAISE WARNING '❌ WARNING: wallet_id column missing from transactions table';
    END IF;

    IF constraint_exists THEN
        RAISE NOTICE '✅ SUCCESS: Foreign key constraint exists';
    ELSE
        RAISE WARNING '❌ WARNING: Foreign key constraint missing';
    END IF;

    RAISE NOTICE '🎉 User-Owned DasWos Coins Migration completed successfully!';
    RAISE NOTICE '📝 Users now own their DasWos coins balance';
    RAISE NOTICE '🔗 Wallets serve as interface for accessing user balance';
    RAISE NOTICE '🔐 Both user authentication and wallet connection provide full functionality';
END $$;

-- Commit the transaction
COMMIT;

-- =====================================================
-- POST-MIGRATION NOTES:
-- =====================================================
-- 1. Users now own their DasWos coins balance (stored in user account)
-- 2. Wallets are interfaces for accessing and using user balance
-- 3. User authentication required to access balance
-- 4. Wallet connection recommended for transactions (audit trail)
-- 5. All transactions track which wallet was used (wallet_id)
-- 6. Balance persists across different wallet connections
-- 7. Clear separation between authentication and interface
-- =====================================================
