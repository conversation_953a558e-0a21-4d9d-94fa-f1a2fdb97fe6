import React, { useState, useEffect } from 'react';
import { X, ShoppingCart, Heart, Star, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  rating: number;
  description: string;
  category: string;
  inStock: boolean;
}

interface FullscreenProductDisplayProps {
  isVisible: boolean;
  searchQuery?: string;
  onClose: () => void;
  className?: string;
}

const FullscreenProductDisplay: React.FC<FullscreenProductDisplayProps> = ({
  isVisible,
  searchQuery,
  onClose,
  className = ''
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Mock product data - in real app this would come from API
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'Premium Wireless Headphones',
      price: 299.99,
      image: '/api/placeholder/300/300',
      rating: 4.8,
      description: 'High-quality wireless headphones with noise cancellation',
      category: 'Electronics',
      inStock: true
    },
    {
      id: '2',
      name: 'Smart Fitness Watch',
      price: 199.99,
      image: '/api/placeholder/300/300',
      rating: 4.6,
      description: 'Advanced fitness tracking with heart rate monitoring',
      category: 'Wearables',
      inStock: true
    },
    {
      id: '3',
      name: 'Organic Coffee Beans',
      price: 24.99,
      image: '/api/placeholder/300/300',
      rating: 4.9,
      description: 'Premium organic coffee beans from sustainable farms',
      category: 'Food & Beverage',
      inStock: true
    },
    {
      id: '4',
      name: 'Ergonomic Office Chair',
      price: 449.99,
      image: '/api/placeholder/300/300',
      rating: 4.7,
      description: 'Comfortable ergonomic chair for long work sessions',
      category: 'Furniture',
      inStock: false
    }
  ];

  // Simulate product search based on query
  useEffect(() => {
    if (isVisible && searchQuery) {
      setIsLoading(true);
      
      // Simulate API delay
      setTimeout(() => {
        const filteredProducts = mockProducts.filter(product =>
          product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
          product.description.toLowerCase().includes(searchQuery.toLowerCase())
        );
        
        setProducts(filteredProducts.length > 0 ? filteredProducts : mockProducts.slice(0, 2));
        setIsLoading(false);
      }, 1000);
    }
  }, [isVisible, searchQuery]);

  if (!isVisible) return null;

  return (
    <div className={`fixed inset-0 bg-black/90 backdrop-blur-sm z-[100] flex items-center justify-center p-4 ${className}`}>
      <div className="w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="text-white">
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <Zap className="h-8 w-8 text-blue-400" />
              Daswos AI Search Results
            </h1>
            {searchQuery && (
              <p className="text-gray-300 mt-2">
                Showing results for: <span className="text-blue-400 font-semibold">"{searchQuery}"</span>
              </p>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-white hover:bg-white/10"
          >
            <X className="h-6 w-6" />
          </Button>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-white text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
              <p className="text-lg">Finding the best products for you...</p>
            </div>
          </div>
        )}

        {/* Products Grid */}
        {!isLoading && products.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <Card key={product.id} className="bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/15 transition-all duration-300">
                <CardHeader className="pb-3">
                  <div className="aspect-square bg-gray-200 rounded-lg mb-3 overflow-hidden">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardTitle className="text-lg line-clamp-2">{product.name}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge variant={product.inStock ? "default" : "destructive"} className="text-xs">
                      {product.inStock ? "In Stock" : "Out of Stock"}
                    </Badge>
                    <Badge variant="outline" className="text-xs text-gray-300 border-gray-300">
                      {product.category}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-gray-300 text-sm mb-4 line-clamp-2">{product.description}</p>
                  
                  <div className="flex items-center gap-2 mb-4">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(product.rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-400'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-300">({product.rating})</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-green-400">
                      ${product.price}
                    </span>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-white hover:bg-white/10"
                      >
                        <Heart className="h-4 w-4" />
                      </Button>
                      <Button
                        size="sm"
                        disabled={!product.inStock}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        Add to Cart
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* No Results */}
        {!isLoading && products.length === 0 && (
          <div className="text-center py-12 text-white">
            <p className="text-xl mb-2">No products found</p>
            <p className="text-gray-300">Try a different search term or browse our categories</p>
          </div>
        )}

        {/* AI Assistant Info */}
        <div className="mt-8 p-4 bg-blue-600/20 rounded-lg border border-blue-400/30">
          <div className="flex items-center gap-2 mb-2">
            <Zap className="h-5 w-5 text-blue-400" />
            <span className="text-white font-semibold">Daswos AI Assistant</span>
          </div>
          <p className="text-gray-300 text-sm">
            Say "Daswos" followed by your command to search for products, get recommendations, or ask questions about items.
            Try commands like "Daswos find me headphones" or "Daswos show me electronics".
          </p>
        </div>
      </div>
    </div>
  );
};

export default FullscreenProductDisplay;
