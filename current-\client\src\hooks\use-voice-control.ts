import { useState, useRef, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useLocation } from 'wouter';

interface VoiceResponse {
  intent: string;
  action?: string;
  parameters?: any;
  response: string;
  confidence: number;
}

interface VoiceControlOptions {
  enableTextToSpeech?: boolean;
}

export const useVoiceControl = (options: VoiceControlOptions = {}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isSupported, setIsSupported] = useState(true);
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [transcript, setTranscript] = useState('');
  const [voiceResponse, setVoiceResponse] = useState<VoiceResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const recognitionRef = useRef<any>(null);
  const [, setLocation] = useLocation();
  const { toast } = useToast();

  // Check if Web Speech API is supported
  useEffect(() => {
    const checkSupport = () => {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

      if (!SpeechRecognition) {
        console.log('❌ Web Speech API not supported in this browser');
        setIsSupported(false);
        setError('Speech recognition not supported in this browser');
      } else {
        console.log('✅ Web Speech API is supported');
        setIsSupported(true);
      }
    };

    checkSupport();
  }, []);

  // Check microphone permission
  const checkMicrophonePermission = useCallback(async (): Promise<boolean> => {
    try {
      console.log('🎤 Checking microphone permission...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      stream.getTracks().forEach(track => track.stop());
      console.log('✅ Microphone permission granted');
      setHasPermission(true);
      setError(null);
      
      toast({
        title: 'Microphone Access Granted',
        description: 'You can now use voice commands!',
      });
      
      return true;
    } catch (mediaError: any) {
      console.error('❌ Microphone access denied:', mediaError);
      setHasPermission(false);

      let errorMessage = 'Microphone access denied.';
      if (mediaError.name === 'NotAllowedError') {
        errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings.';
      } else if (mediaError.name === 'NotFoundError') {
        errorMessage = 'No microphone found. Please connect a microphone and try again.';
      } else if (mediaError.name === 'NotReadableError') {
        errorMessage = 'Microphone is being used by another application.';
      }

      setError(errorMessage);
      toast({
        title: 'Microphone Access Required',
        description: errorMessage,
        variant: 'destructive',
      });
      
      return false;
    }
  }, [toast]);

  // Local pattern matching for voice commands
  const processVoiceCommand = useCallback((input: string): VoiceResponse => {
    const lowerInput = input.toLowerCase().trim();
    console.log('🔍 Processing voice command:', lowerInput);

    // Remove wake word variations
    const cleanInput = lowerInput
      .replace(/^(daswos|das wos|das was|das boss|das voice|das worth|das worse|das west|daswo|dazwas|daz wos)\s*/i, '')
      .trim();

    console.log('🧹 Cleaned input:', cleanInput);

    // Navigation patterns
    const navigationPatterns = [
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?profile/i, route: '/profile', name: 'profile' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:shopping\s+)?cart/i, route: '/cart', name: 'cart' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?orders/i, route: '/orders', name: 'orders' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:auto\s*shop|autoshop)/i, route: '/autoshop-dashboard', name: 'AutoShop dashboard' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:my\s+)?(?:daswos\s+)?coins/i, route: '/daswos-coins', name: 'DasWos Coins' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+(?:the\s+)?home(?:\s+page)?/i, route: '/', name: 'home' },
      { pattern: /(?:go to|navigate to|open|show me|take me to)\s+settings/i, route: '/settings', name: 'settings' },
    ];

    for (const { pattern, route, name } of navigationPatterns) {
      if (pattern.test(cleanInput)) {
        return {
          intent: 'navigation',
          action: 'navigate',
          parameters: { route },
          response: `Navigating to your ${name}...`,
          confidence: 0.9
        };
      }
    }

    // AutoShop patterns
    const autoShopPatterns = [
      /(?:start|enable|activate|begin|turn on)\s+(?:auto\s*shop|autoshop)/i,
      /(?:let's|lets)\s+(?:auto\s*shop|autoshop)/i,
      /(?:auto\s*shop|autoshop)\s+(?:start|begin|go|now)/i,
      /(?:start|begin)\s+(?:automatic\s+)?shopping/i,
      /(?:shop for me|shop automatically)/i,
    ];

    for (const pattern of autoShopPatterns) {
      if (pattern.test(cleanInput)) {
        return {
          intent: 'autoshop',
          action: 'start_autoshop',
          parameters: {},
          response: 'Starting AutoShop for you. I\'ll help you find and purchase items automatically!',
          confidence: 0.9
        };
      }
    }

    // Search patterns
    const searchPatterns = [
      /(?:search for|find|look for|show me|find me)\s+(.+)/i,
      /(?:search)\s+(.+)/i,
      /(?:i want to buy|i need|get me|buy me|purchase)\s+(.+)/i,
      /(?:can you find|help me find|locate)\s+(.+)/i,
      /(?:what about|how about|show me some)\s+(.+)/i,
      /(?:i'm looking for|looking for)\s+(.+)/i,
    ];

    for (const pattern of searchPatterns) {
      const match = cleanInput.match(pattern);
      if (match && match[1]) {
        const searchQuery = match[1].trim().replace(/\s+(please|now|today)$/i, '');
        return {
          intent: 'search',
          action: 'search',
          parameters: { query: searchQuery },
          response: `Searching for ${searchQuery}...`,
          confidence: 0.8
        };
      }
    }

    // Greeting patterns
    if (/^(hi|hello|hey|good morning|good afternoon|good evening)$/i.test(cleanInput)) {
      return {
        intent: 'conversation',
        response: 'Hello! I\'m DasWos AI, your intelligent shopping assistant. You can ask me to search for products, navigate the site, or start AutoShop. How can I help you today?',
        confidence: 0.9
      };
    }

    // Help patterns
    if (/^(help|what can you do|commands)$/i.test(cleanInput)) {
      return {
        intent: 'conversation',
        response: 'I can help you with:\n• Search for products: "Find me shoes"\n• Navigate: "Go to my profile"\n• Start AutoShop: "Let\'s AutoShop"\n• General questions about DasWos\n\nWhat would you like to do?',
        confidence: 0.9
      };
    }

    // Default response
    return {
      intent: 'conversation',
      response: 'I\'m not sure I understood that. You can ask me to search for products, navigate to different pages, or start AutoShop. Try saying something like "search for shoes" or "go to my profile".',
      confidence: 0.3
    };
  }, []);

  // Handle voice actions
  const handleVoiceAction = useCallback((response: VoiceResponse) => {
    console.log('🎯 Handling voice action:', response);

    if (response.intent === 'search' && response.parameters?.query) {
      console.log('🔍 Performing search:', response.parameters.query);
      
      // Dispatch search events
      const searchEvent = new CustomEvent('aiSearch', {
        detail: { query: response.parameters.query }
      });
      window.dispatchEvent(searchEvent);

      const voiceSearchEvent = new CustomEvent('voiceSearch', {
        detail: { query: response.parameters.query }
      });
      window.dispatchEvent(voiceSearchEvent);

    } else if (response.intent === 'navigation' && response.parameters?.route) {
      console.log('🧭 Navigating to:', response.parameters.route);
      setLocation(response.parameters.route);
      
    } else if (response.intent === 'autoshop') {
      console.log('🛒 Starting AutoShop');
      const autoshopEvent = new CustomEvent('aiAutoshop');
      window.dispatchEvent(autoshopEvent);
    }
  }, [setLocation]);

  // Text-to-speech using browser API
  const speakResponse = useCallback((text: string) => {
    if (!options.enableTextToSpeech) return;

    try {
      console.log('🔊 Speaking response:', text);
      
      // Cancel any ongoing speech
      speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      utterance.volume = 0.8;

      // Try to use a better voice
      const voices = speechSynthesis.getVoices();
      const preferredVoice = voices.find(voice =>
        voice.name.includes('Google') ||
        voice.name.includes('Microsoft') ||
        voice.lang.startsWith('en')
      );
      if (preferredVoice) {
        utterance.voice = preferredVoice;
      }

      utterance.onstart = () => {
        const speakingEvent = new CustomEvent('voiceStatus', {
          detail: { status: 'speaking', message: '🗣️ DasWos AI is responding...' }
        });
        window.dispatchEvent(speakingEvent);
      };

      utterance.onend = () => {
        const idleEvent = new CustomEvent('voiceStatus', {
          detail: { status: 'idle', message: '' }
        });
        window.dispatchEvent(idleEvent);
      };

      speechSynthesis.speak(utterance);
    } catch (error) {
      console.error('❌ Speech synthesis error:', error);
    }
  }, [options.enableTextToSpeech]);

  // Start voice recording
  const startRecording = useCallback(async () => {
    console.log('🎯 Starting voice recording...');

    // Check microphone permissions first
    if (hasPermission === null) {
      console.log('🎤 Checking microphone permissions first...');
      const permissionGranted = await checkMicrophonePermission();
      if (!permissionGranted) {
        console.log('❌ Microphone permission denied');
        return;
      }
    }

    const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;

    if (!SpeechRecognition) {
      console.log('❌ Web Speech API not supported');
      setError('Speech recognition not supported in this browser');
      return;
    }

    try {
      const recognition = new SpeechRecognition();
      recognitionRef.current = recognition;

      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';

      let finalTranscript = '';
      let hasDetectedSpeech = false;

      recognition.onstart = () => {
        console.log('🎤 Speech recognition started');
        setIsRecording(true);
        setError(null);
        setTranscript('');
        setVoiceResponse(null);

        // Emit voice status event
        const statusEvent = new CustomEvent('voiceStatus', {
          detail: { status: 'listening', message: '🎤 Listening for "Daswos"...' }
        });
        window.dispatchEvent(statusEvent);

        toast({
          title: 'DasWos AI Activated',
          description: 'Say "Daswos" followed by your command...',
        });
      };

      recognition.onresult = (event: any) => {
        console.log('📝 Speech recognition result:', event);
        let interim = '';
        let final = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            final += transcript;
          } else {
            interim += transcript;
          }
        }

        if (interim) {
          setTranscript(`Hearing: "${interim}"`);
          hasDetectedSpeech = true;
        }

        if (final) {
          finalTranscript = final;
          console.log('✅ Final transcript:', final);

          // Check if the command contains "Daswos" wake word
          const lowerTranscript = final.toLowerCase();
          const wakeWordPatterns = [
            'daswos', 'das wos', 'das was', 'das boss', 'das voice',
            'das worth', 'das worse', 'das west', 'daswo', 'dazwas', 'daz wos'
          ];

          const containsDaswos = wakeWordPatterns.some(pattern =>
            lowerTranscript.includes(pattern)
          );

          if (containsDaswos) {
            setTranscript(`You said: "${final}"`);

            // Emit processing status
            const statusEvent = new CustomEvent('voiceStatus', {
              detail: { status: 'processing', message: '🤖 Processing your request...' }
            });
            window.dispatchEvent(statusEvent);

            // Process the command
            setIsProcessing(true);
            const response = processVoiceCommand(final);
            setVoiceResponse(response);

            // Handle the action
            handleVoiceAction(response);

            // Speak the response
            speakResponse(response.response);

            setIsProcessing(false);
            recognition.stop();

            toast({
              title: 'DasWos AI Voice Command',
              description: `"${final}" → ${response.response}`,
            });

          } else {
            // No wake word detected, continue listening
            setTranscript('Listening for "Daswos"... Say "Daswos" followed by your command.');
            console.log('⚠️ No wake word detected in:', final);
          }
        }
      };

      recognition.onerror = (event: any) => {
        console.error('❌ Speech recognition error:', event.error);
        setError(`Speech recognition error: ${event.error}`);
        setIsRecording(false);

        toast({
          title: 'Speech Recognition Error',
          description: `Error: ${event.error}`,
          variant: 'destructive',
        });
      };

      recognition.onend = () => {
        console.log('🛑 Speech recognition ended');
        setIsRecording(false);

        // Emit idle status
        const idleEvent = new CustomEvent('voiceStatus', {
          detail: { status: 'idle', message: '' }
        });
        window.dispatchEvent(idleEvent);
      };

      recognition.start();

      // Auto-stop after 10 seconds for better responsiveness
      setTimeout(() => {
        if (recognition && recognitionRef.current === recognition) {
          recognition.stop();
        }
      }, 10000);

    } catch (err: any) {
      console.error('❌ Failed to start speech recognition:', err);
      setError('Failed to start speech recognition');
      setIsRecording(false);

      toast({
        title: 'Speech Recognition Error',
        description: 'Failed to start speech recognition. Please try again.',
        variant: 'destructive',
      });
    }
  }, [hasPermission, checkMicrophonePermission, processVoiceCommand, handleVoiceAction, speakResponse, toast]);

  // Stop voice recording
  const stopRecording = useCallback(() => {
    console.log('🛑 Stopping voice recording...');

    if (recognitionRef.current) {
      recognitionRef.current.stop();
      console.log('🛑 Speech recognition stopped');
    }

    setIsRecording(false);

    // Emit idle status
    const statusEvent = new CustomEvent('voiceStatus', {
      detail: { status: 'idle', message: '' }
    });
    window.dispatchEvent(statusEvent);
  }, []);

  return {
    isRecording,
    isProcessing,
    isSupported,
    hasPermission,
    transcript,
    voiceResponse,
    error,
    startRecording,
    stopRecording,
    checkMicrophonePermission,
    processVoiceCommand,
    handleVoiceAction,
    speakResponse
  };
};
