import React, { useState } from 'react';
import { useLocation } from 'wouter';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Wallet,
  Plus,
  Minus,
  ArrowUpRight,
  ArrowDownLeft,
  History,
  Settings,
  LogOut,
  ChevronRight,
  Copy,
  Check
} from 'lucide-react';
import { useWallet, walletUtils } from '@/hooks/use-wallet';
import { DasWosCoinIcon } from '@/components/daswos-coin-icon';
import WalletLogin from './wallet-login';
import { useAuth } from '@/hooks/use-auth';
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';

interface WalletDropdownProps {
  className?: string;
}

export default function WalletDropdown({ className }: WalletDropdownProps) {
  const [, setLocation] = useLocation();
  const { wallet, isWalletConnected, logoutWallet } = useWallet();
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [showWalletLogin, setShowWalletLogin] = useState(false);
  const [copiedWalletId, setCopiedWalletId] = useState(false);

  // Fetch DasWos coins balance for logged in users
  const { data: coinsData, isLoading: coinsLoading, error: coinsError } = useQuery({
    queryKey: ['/api/user/daswos-coins/balance', wallet?.wallet_id],
    queryFn: async () => {
      if (!wallet?.wallet_id) {
        throw new Error('Wallet connection required to access balance');
      }

      const url = `/api/user/daswos-coins/balance?wallet_id=${encodeURIComponent(wallet.wallet_id)}&_t=${Date.now()}`;

      console.log('🔍 Fetching balance from:', url);

      const result = await apiRequest(url, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      console.log('💰 Balance result:', result);
      return result;
    },
    enabled: !!user && isWalletConnected && !!wallet?.wallet_id, // Only fetch if user is logged in and wallet is connected
    staleTime: 30000, // 30 seconds
    refetchInterval: (!!user && isWalletConnected && !!wallet?.wallet_id) ? 30000 : false, // Only refetch when enabled
    retry: 3,
    retryDelay: 1000,
  });

  // Debug logging
  console.log('🔍 Wallet Dropdown Debug:', {
    user: !!user,
    wallet: wallet?.wallet_id,
    isWalletConnected,
    coinsData,
    coinsLoading,
    coinsError: coinsError?.message,
    queryEnabled: !!user && isWalletConnected && !!wallet?.wallet_id
  });

  // Purchase DasWos coins mutation
  const purchaseMutation = useMutation({
    mutationFn: async (amount: number) => {
      if (!wallet?.wallet_id) {
        throw new Error('Wallet connection required to add funds');
      }

      const requestData = {
        amount,
        wallet_id: wallet.wallet_id, // REQUIRED: Include wallet ID for verification
        metadata: {
          packageName: `${amount} DasWos Coins`,
          purchaseTimestamp: new Date().toISOString(),
          source: 'wallet_add_funds',
          walletUsed: wallet.wallet_id
        }
      };

      console.log('💰 Sending purchase request:', requestData);

      // Use the new apiRequest signature for better reliability
      return apiRequest('POST', '/api/user/daswos-coins/purchase', requestData, {
        credentials: 'include'
      });
    },
    onSuccess: (data, amount) => {
      toast({
        title: 'Funds Added',
        description: `Successfully added ${amount.toLocaleString()} DasWos Coins to your wallet`,
      });
      // Refresh the balance
      queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });
    },
    onError: (error) => {
      toast({
        title: 'Purchase Failed',
        description: 'Failed to add funds. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleCopyWalletId = async () => {
    if (wallet?.wallet_id) {
      try {
        await navigator.clipboard.writeText(wallet.wallet_id);
        setCopiedWalletId(true);
        setTimeout(() => setCopiedWalletId(false), 2000);
      } catch (error) {
        console.error('Failed to copy wallet ID:', error);
      }
    }
  };

  const handleNavigation = (path: string) => {
    setLocation(path);
  };

  // If no wallet is connected, show the wallet button that opens login
  if (!isWalletConnected) {
    return (
      <>
        <Button
          onClick={() => setShowWalletLogin(true)}
          className="bg-[#E0E0E0] hover:bg-gray-200 px-2 py-1 border border-gray-300 text-black flex items-center text-xs h-auto"
        >
          <Wallet className="h-4 w-4 mr-1" />
          <span>wallet</span>
        </Button>

        {showWalletLogin && (
          <div className="fixed inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="relative">
              <WalletLogin onClose={() => setShowWalletLogin(false)} />
            </div>
          </div>
        )}
      </>
    );
  }

  // If wallet is connected, show the wallet dropdown
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button className="bg-[#E0E0E0] hover:bg-gray-200 px-2 py-1 border border-gray-300 text-black flex items-center text-xs h-auto">
          <Wallet className="h-4 w-4 mr-1" />
          <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
          <span>wallet</span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        align="end"
        className="bg-[#E0E0E0] text-black p-1 border border-gray-300 rounded-none shadow-md w-80 user-dropdown"
      >
        {/* Wallet Header */}
        <div className="border-b border-gray-300 py-3 px-3 bg-gray-100">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-medium">DasWos Wallet</h3>
            <Badge variant="outline" className="text-xs bg-green-100 text-green-800 border-green-300">
              Connected
            </Badge>
          </div>

          {/* Wallet ID */}
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-600">Wallet ID:</span>
            <div className="flex items-center">
              <span className="text-xs font-mono mr-1">{wallet?.wallet_id}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCopyWalletId}
                className="h-4 w-4 p-0 hover:bg-gray-200"
              >
                {copiedWalletId ? (
                  <Check className="h-3 w-3 text-green-600" />
                ) : (
                  <Copy className="h-3 w-3" />
                )}
              </Button>
            </div>
          </div>

          {/* Balance */}
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Balance:</span>
            <div className="flex items-center">
              <DasWosCoinIcon size={14} className="mr-1" />
              <span className="text-sm font-medium">
                {coinsLoading ? (
                  'Loading...'
                ) : coinsError ? (
                  'Error'
                ) : coinsData && typeof coinsData.balance === 'number' ? (
                  coinsData.balance.toLocaleString()
                ) : (
                  '0'
                )}
              </span>
            </div>
          </div>
          {!user ? (
            <div className="text-xs text-red-600 mt-1">
              ⚠️ Please sign in to your DasWos account first
            </div>
          ) : coinsError && coinsError.message.includes('must be logged') ? (
            <div className="text-xs text-red-600 mt-1">
              ⚠️ Authentication required - please refresh and sign in
            </div>
          ) : coinsError ? (
            <div className="text-xs text-red-600 mt-1">
              Failed to load balance: {coinsError.message}
            </div>
          ) : coinsLoading ? (
            <div className="text-xs text-yellow-600 mt-1">
              Loading balance...
            </div>
          ) : user && wallet && coinsData ? (
            <div className="text-xs text-gray-500 mt-1">
              Synced with your DasWos account
            </div>
          ) : user && wallet ? (
            <div className="text-xs text-orange-600 mt-1">
              Waiting for balance data...
            </div>
          ) : (
            <div className="text-xs text-gray-500 mt-1">
              Connect wallet to access balance
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="p-3 border-b border-gray-300">
          <h4 className="text-xs font-medium mb-2 text-gray-600">Quick Actions</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (!user) {
                  toast({
                    title: 'Login Required',
                    description: 'Please sign in to purchase DasWos Coins',
                    variant: 'destructive',
                  });
                  return;
                }
                if (!wallet) {
                  toast({
                    title: 'Wallet Required',
                    description: 'Please connect your wallet to purchase DasWos Coins',
                    variant: 'destructive',
                  });
                  return;
                }

                // Prompt user for amount
                const amountStr = prompt('Enter amount of DasWos Coins to add (1 coin = $1):');
                if (amountStr) {
                  const amount = parseInt(amountStr);
                  if (isNaN(amount) || amount <= 0) {
                    toast({
                      title: 'Invalid Amount',
                      description: 'Please enter a valid positive number',
                      variant: 'destructive',
                    });
                    return;
                  }
                  if (amount > 10000) {
                    toast({
                      title: 'Amount Too Large',
                      description: 'Maximum amount is 10,000 coins per transaction',
                      variant: 'destructive',
                    });
                    return;
                  }
                  // Purchase the specified amount of DasWos coins
                  purchaseMutation.mutate(amount);
                }
              }}
              disabled={purchaseMutation.isPending || !user || !wallet}
              className="text-xs h-8 bg-white hover:bg-gray-50 border-gray-300"
            >
              <Plus className="h-3 w-3 mr-1" />
              {purchaseMutation.isPending ? 'Adding...' : 'Add Funds'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // TODO: Implement send funds functionality
                console.log('Send funds clicked');
              }}
              className="text-xs h-8 bg-white hover:bg-gray-50 border-gray-300"
            >
              <ArrowUpRight className="h-3 w-3 mr-1" />
              Send
            </Button>
          </div>
        </div>

        {/* Menu Items */}
        <div className="py-1">
          <DropdownMenuItem
            onClick={() => {
              // TODO: Navigate to transaction history
              console.log('Transaction history clicked');
            }}
            className="py-2 px-3 text-xs hover:bg-gray-200 rounded-none flex items-center user-menu-item"
          >
            <History className="mr-2 h-3 w-3" />
            <span>Transaction History</span>
            <ChevronRight className="ml-auto h-3 w-3" />
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={() => {
              // TODO: Navigate to wallet settings
              console.log('Wallet settings clicked');
            }}
            className="py-2 px-3 text-xs hover:bg-gray-200 rounded-none flex items-center user-menu-item"
          >
            <Settings className="mr-2 h-3 w-3" />
            <span>Wallet Settings</span>
            <ChevronRight className="ml-auto h-3 w-3" />
          </DropdownMenuItem>

          <DropdownMenuSeparator className="bg-gray-300" />

          <DropdownMenuItem
            onClick={() => {
              logoutWallet();
              console.log('Wallet disconnected');
            }}
            className="py-2 px-3 text-xs hover:bg-red-100 rounded-none flex items-center user-menu-item text-red-600"
          >
            <LogOut className="mr-2 h-3 w-3" />
            <span>Disconnect Wallet</span>
          </DropdownMenuItem>
        </div>

        {/* Footer Info */}
        <div className="border-t border-gray-300 py-2 px-3 bg-gray-50">
          <p className="text-xs text-gray-500 text-center">
            Wallet connected to DasWos ecosystem
          </p>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
