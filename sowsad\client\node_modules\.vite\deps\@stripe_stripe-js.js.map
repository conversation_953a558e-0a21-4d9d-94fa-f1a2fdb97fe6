{"version": 3, "sources": ["../../../../node_modules/@stripe/stripe-js/dist/index.mjs"], "sourcesContent": ["var RELEASE_TRAIN = 'acacia';\n\nvar runtimeVersionToUrlVersion = function runtimeVersionToUrlVersion(version) {\n  return version === 3 ? 'v3' : version;\n};\n\nvar ORIGIN = 'https://js.stripe.com';\nvar STRIPE_JS_URL = \"\".concat(ORIGIN, \"/\").concat(RELEASE_TRAIN, \"/stripe.js\");\nvar V3_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/v3\\/?(\\?.*)?$/;\nvar STRIPE_JS_URL_REGEX = /^https:\\/\\/js\\.stripe\\.com\\/(v3|[a-z]+)\\/stripe\\.js(\\?.*)?$/;\nvar EXISTING_SCRIPT_MESSAGE = 'loadStripe.setLoadParameters was called but an existing Stripe.js script already exists in the document; existing script parameters will be used';\n\nvar isStripeJSURL = function isStripeJSURL(url) {\n  return V3_URL_REGEX.test(url) || STRIPE_JS_URL_REGEX.test(url);\n};\n\nvar findScript = function findScript() {\n  var scripts = document.querySelectorAll(\"script[src^=\\\"\".concat(ORIGIN, \"\\\"]\"));\n\n  for (var i = 0; i < scripts.length; i++) {\n    var script = scripts[i];\n\n    if (!isStripeJSURL(script.src)) {\n      continue;\n    }\n\n    return script;\n  }\n\n  return null;\n};\n\nvar injectScript = function injectScript(params) {\n  var queryString = params && !params.advancedFraudSignals ? '?advancedFraudSignals=false' : '';\n  var script = document.createElement('script');\n  script.src = \"\".concat(STRIPE_JS_URL).concat(queryString);\n  var headOrBody = document.head || document.body;\n\n  if (!headOrBody) {\n    throw new Error('Expected document.body not to be null. Stripe.js requires a <body> element.');\n  }\n\n  headOrBody.appendChild(script);\n  return script;\n};\n\nvar registerWrapper = function registerWrapper(stripe, startTime) {\n  if (!stripe || !stripe._registerWrapper) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'stripe-js',\n    version: \"6.1.0\",\n    startTime: startTime\n  });\n};\n\nvar stripePromise$1 = null;\nvar onErrorListener = null;\nvar onLoadListener = null;\n\nvar onError = function onError(reject) {\n  return function (cause) {\n    reject(new Error('Failed to load Stripe.js', {\n      cause: cause\n    }));\n  };\n};\n\nvar onLoad = function onLoad(resolve, reject) {\n  return function () {\n    if (window.Stripe) {\n      resolve(window.Stripe);\n    } else {\n      reject(new Error('Stripe.js not available'));\n    }\n  };\n};\n\nvar loadScript = function loadScript(params) {\n  // Ensure that we only attempt to load Stripe.js at most once\n  if (stripePromise$1 !== null) {\n    return stripePromise$1;\n  }\n\n  stripePromise$1 = new Promise(function (resolve, reject) {\n    if (typeof window === 'undefined' || typeof document === 'undefined') {\n      // Resolve to null when imported server side. This makes the module\n      // safe to import in an isomorphic code base.\n      resolve(null);\n      return;\n    }\n\n    if (window.Stripe && params) {\n      console.warn(EXISTING_SCRIPT_MESSAGE);\n    }\n\n    if (window.Stripe) {\n      resolve(window.Stripe);\n      return;\n    }\n\n    try {\n      var script = findScript();\n\n      if (script && params) {\n        console.warn(EXISTING_SCRIPT_MESSAGE);\n      } else if (!script) {\n        script = injectScript(params);\n      } else if (script && onLoadListener !== null && onErrorListener !== null) {\n        var _script$parentNode;\n\n        // remove event listeners\n        script.removeEventListener('load', onLoadListener);\n        script.removeEventListener('error', onErrorListener); // if script exists, but we are reloading due to an error,\n        // reload script to trigger 'load' event\n\n        (_script$parentNode = script.parentNode) === null || _script$parentNode === void 0 ? void 0 : _script$parentNode.removeChild(script);\n        script = injectScript(params);\n      }\n\n      onLoadListener = onLoad(resolve, reject);\n      onErrorListener = onError(reject);\n      script.addEventListener('load', onLoadListener);\n      script.addEventListener('error', onErrorListener);\n    } catch (error) {\n      reject(error);\n      return;\n    }\n  }); // Resets stripePromise on error\n\n  return stripePromise$1[\"catch\"](function (error) {\n    stripePromise$1 = null;\n    return Promise.reject(error);\n  });\n};\nvar initStripe = function initStripe(maybeStripe, args, startTime) {\n  if (maybeStripe === null) {\n    return null;\n  }\n\n  var pk = args[0];\n  var isTestKey = pk.match(/^pk_test/); // @ts-expect-error this is not publicly typed\n\n  var version = runtimeVersionToUrlVersion(maybeStripe.version);\n  var expectedVersion = RELEASE_TRAIN;\n\n  if (isTestKey && version !== expectedVersion) {\n    console.warn(\"Stripe.js@\".concat(version, \" was loaded on the page, but @stripe/stripe-js@\").concat(\"6.1.0\", \" expected Stripe.js@\").concat(expectedVersion, \". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning\"));\n  }\n\n  var stripe = maybeStripe.apply(undefined, args);\n  registerWrapper(stripe, startTime);\n  return stripe;\n}; // eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\n\nvar stripePromise;\nvar loadCalled = false;\n\nvar getStripePromise = function getStripePromise() {\n  if (stripePromise) {\n    return stripePromise;\n  }\n\n  stripePromise = loadScript(null)[\"catch\"](function (error) {\n    // clear cache on error\n    stripePromise = null;\n    return Promise.reject(error);\n  });\n  return stripePromise;\n}; // Execute our own script injection after a tick to give users time to do their\n// own script injection.\n\n\nPromise.resolve().then(function () {\n  return getStripePromise();\n})[\"catch\"](function (error) {\n  if (!loadCalled) {\n    console.warn(error);\n  }\n});\nvar loadStripe = function loadStripe() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  loadCalled = true;\n  var startTime = Date.now(); // if previous attempts are unsuccessful, will re-load script\n\n  return getStripePromise().then(function (maybeStripe) {\n    return initStripe(maybeStripe, args, startTime);\n  });\n};\n\nexport { loadStripe };\n"], "mappings": ";;;AAAA,IAAI,gBAAgB;AAEpB,IAAI,6BAA6B,SAASA,4BAA2B,SAAS;AAC5E,SAAO,YAAY,IAAI,OAAO;AAChC;AAEA,IAAI,SAAS;AACb,IAAI,gBAAgB,GAAG,OAAO,QAAQ,GAAG,EAAE,OAAO,eAAe,YAAY;AAC7E,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,0BAA0B;AAE9B,IAAI,gBAAgB,SAASC,eAAc,KAAK;AAC9C,SAAO,aAAa,KAAK,GAAG,KAAK,oBAAoB,KAAK,GAAG;AAC/D;AAEA,IAAI,aAAa,SAASC,cAAa;AACrC,MAAI,UAAU,SAAS,iBAAiB,gBAAiB,OAAO,QAAQ,IAAK,CAAC;AAE9E,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,QAAI,SAAS,QAAQ,CAAC;AAEtB,QAAI,CAAC,cAAc,OAAO,GAAG,GAAG;AAC9B;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO;AACT;AAEA,IAAI,eAAe,SAASC,cAAa,QAAQ;AAC/C,MAAI,cAAc,UAAU,CAAC,OAAO,uBAAuB,gCAAgC;AAC3F,MAAI,SAAS,SAAS,cAAc,QAAQ;AAC5C,SAAO,MAAM,GAAG,OAAO,aAAa,EAAE,OAAO,WAAW;AACxD,MAAI,aAAa,SAAS,QAAQ,SAAS;AAE3C,MAAI,CAAC,YAAY;AACf,UAAM,IAAI,MAAM,6EAA6E;AAAA,EAC/F;AAEA,aAAW,YAAY,MAAM;AAC7B,SAAO;AACT;AAEA,IAAI,kBAAkB,SAASC,iBAAgB,QAAQ,WAAW;AAChE,MAAI,CAAC,UAAU,CAAC,OAAO,kBAAkB;AACvC;AAAA,EACF;AAEA,SAAO,iBAAiB;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AACH;AAEA,IAAI,kBAAkB;AACtB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AAErB,IAAI,UAAU,SAASC,SAAQ,QAAQ;AACrC,SAAO,SAAU,OAAO;AACtB,WAAO,IAAI,MAAM,4BAA4B;AAAA,MAC3C;AAAA,IACF,CAAC,CAAC;AAAA,EACJ;AACF;AAEA,IAAI,SAAS,SAASC,QAAO,SAAS,QAAQ;AAC5C,SAAO,WAAY;AACjB,QAAI,OAAO,QAAQ;AACjB,cAAQ,OAAO,MAAM;AAAA,IACvB,OAAO;AACL,aAAO,IAAI,MAAM,yBAAyB,CAAC;AAAA,IAC7C;AAAA,EACF;AACF;AAEA,IAAI,aAAa,SAASC,YAAW,QAAQ;AAE3C,MAAI,oBAAoB,MAAM;AAC5B,WAAO;AAAA,EACT;AAEA,oBAAkB,IAAI,QAAQ,SAAU,SAAS,QAAQ;AACvD,QAAI,OAAO,WAAW,eAAe,OAAO,aAAa,aAAa;AAGpE,cAAQ,IAAI;AACZ;AAAA,IACF;AAEA,QAAI,OAAO,UAAU,QAAQ;AAC3B,cAAQ,KAAK,uBAAuB;AAAA,IACtC;AAEA,QAAI,OAAO,QAAQ;AACjB,cAAQ,OAAO,MAAM;AACrB;AAAA,IACF;AAEA,QAAI;AACF,UAAI,SAAS,WAAW;AAExB,UAAI,UAAU,QAAQ;AACpB,gBAAQ,KAAK,uBAAuB;AAAA,MACtC,WAAW,CAAC,QAAQ;AAClB,iBAAS,aAAa,MAAM;AAAA,MAC9B,WAAW,UAAU,mBAAmB,QAAQ,oBAAoB,MAAM;AACxE,YAAI;AAGJ,eAAO,oBAAoB,QAAQ,cAAc;AACjD,eAAO,oBAAoB,SAAS,eAAe;AAGnD,SAAC,qBAAqB,OAAO,gBAAgB,QAAQ,uBAAuB,SAAS,SAAS,mBAAmB,YAAY,MAAM;AACnI,iBAAS,aAAa,MAAM;AAAA,MAC9B;AAEA,uBAAiB,OAAO,SAAS,MAAM;AACvC,wBAAkB,QAAQ,MAAM;AAChC,aAAO,iBAAiB,QAAQ,cAAc;AAC9C,aAAO,iBAAiB,SAAS,eAAe;AAAA,IAClD,SAAS,OAAO;AACd,aAAO,KAAK;AACZ;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO,gBAAgB,OAAO,EAAE,SAAU,OAAO;AAC/C,sBAAkB;AAClB,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC7B,CAAC;AACH;AACA,IAAI,aAAa,SAASC,YAAW,aAAa,MAAM,WAAW;AACjE,MAAI,gBAAgB,MAAM;AACxB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK,KAAK,CAAC;AACf,MAAI,YAAY,GAAG,MAAM,UAAU;AAEnC,MAAI,UAAU,2BAA2B,YAAY,OAAO;AAC5D,MAAI,kBAAkB;AAEtB,MAAI,aAAa,YAAY,iBAAiB;AAC5C,YAAQ,KAAK,aAAa,OAAO,SAAS,iDAAiD,EAAE,OAAO,SAAS,sBAAsB,EAAE,OAAO,iBAAiB,sHAAsH,CAAC;AAAA,EACtR;AAEA,MAAI,SAAS,YAAY,MAAM,QAAW,IAAI;AAC9C,kBAAgB,QAAQ,SAAS;AACjC,SAAO;AACT;AAEA,IAAI;AACJ,IAAI,aAAa;AAEjB,IAAI,mBAAmB,SAASC,oBAAmB;AACjD,MAAI,eAAe;AACjB,WAAO;AAAA,EACT;AAEA,kBAAgB,WAAW,IAAI,EAAE,OAAO,EAAE,SAAU,OAAO;AAEzD,oBAAgB;AAChB,WAAO,QAAQ,OAAO,KAAK;AAAA,EAC7B,CAAC;AACD,SAAO;AACT;AAIA,QAAQ,QAAQ,EAAE,KAAK,WAAY;AACjC,SAAO,iBAAiB;AAC1B,CAAC,EAAE,OAAO,EAAE,SAAU,OAAO;AAC3B,MAAI,CAAC,YAAY;AACf,YAAQ,KAAK,KAAK;AAAA,EACpB;AACF,CAAC;AACD,IAAI,aAAa,SAASC,cAAa;AACrC,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AAEA,eAAa;AACb,MAAI,YAAY,KAAK,IAAI;AAEzB,SAAO,iBAAiB,EAAE,KAAK,SAAU,aAAa;AACpD,WAAO,WAAW,aAAa,MAAM,SAAS;AAAA,EAChD,CAAC;AACH;", "names": ["runtimeVersionToUrlVersion", "isStripeJSURL", "findScript", "injectScript", "registerWrapper", "onError", "onLoad", "loadScript", "initStripe", "getStripePromise", "loadStripe"]}