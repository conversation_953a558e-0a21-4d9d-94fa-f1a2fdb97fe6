import { useState, useCallback, useRef } from 'react';

export type RobotState = 'idle' | 'talk' | 'dance' | 'roll' | 'search' | 'sing' | 'walk' | 'glamour' | 'back' | 'trainers';
export type RobotView = 'front' | 'side' | 'threeQuarter' | 'back' | 'top';

export interface RobotAnimationState {
  // Core state
  robotState: RobotState;
  currentView: RobotView;
  targetView: RobotView;

  // Position and movement
  robotX: number;
  robotY: number;
  targetX: number;
  targetY: number;
  isRolling: boolean;
  rollDirection: number;
  rollSpeed: number;

  // Animation effects
  headRotation: number;
  headBobAmount: number;
  bodyRotation: number;
  bodyRotationSpeed: number;
  armLeftRotation: number;
  armRightRotation: number;
  legsVisible: boolean;
  legsVisibility: number;
  eyeBlinkTime: number;
  isBlinking: boolean;
  talkPulse: number;
  mouthOpenAmount: number;
  mouthAnimation: number;
  dancePhase: number;
  searchAngle: number;

  // View management
  viewTransitionProgress: number;

  // Mouse interaction
  lastMouseX: number;
  lastMouseY: number;
  mouseInteractionTimer: number;

  // Mouse hold and spinning
  isMousePressed: boolean;
  mouseHoldStartTime: number;
  isSpinning: boolean;
  spinRotation: number;
  spinSpeed: number;

  // Scale and positioning
  robotScale: number;
  targetScale: number;
  centerX: number;
  centerY: number;
  shouldReturnToCenter: boolean;
  danceStartX: number;
  danceStartY: number;

  // State timing
  stateStartTime: number;
  previousState: RobotState;
  transitionProgress: number;
  isTransitioning: boolean;
}

const INITIAL_STATE: RobotAnimationState = {
  robotState: 'idle',
  currentView: 'front',
  targetView: 'front',

  robotX: 0,
  robotY: 0,
  targetX: 0,
  targetY: 0,
  isRolling: false,
  rollDirection: 0,
  rollSpeed: 0,

  headRotation: 0,
  headBobAmount: 0,
  bodyRotation: 0,
  bodyRotationSpeed: 0,
  armLeftRotation: 0,
  armRightRotation: 0,
  legsVisible: true,
  legsVisibility: 1,
  eyeBlinkTime: 0,
  isBlinking: false,
  talkPulse: 0,
  mouthOpenAmount: 0,
  mouthAnimation: 0,
  dancePhase: 0,
  searchAngle: 0,

  viewTransitionProgress: 0,

  lastMouseX: 0,
  lastMouseY: 0,
  mouseInteractionTimer: 0,

  isMousePressed: false,
  mouseHoldStartTime: 0,
  isSpinning: false,
  spinRotation: 0,
  spinSpeed: 0,

  robotScale: 0.5,
  targetScale: 0.5,
  centerX: 0,
  centerY: 0,
  shouldReturnToCenter: false,
  danceStartX: 0,
  danceStartY: 0,

  stateStartTime: 0,
  previousState: 'idle',
  transitionProgress: 0,
  isTransitioning: false,
};

export const useRobotAnimation = () => {
  const [state, setState] = useState<RobotAnimationState>(INITIAL_STATE);
  const animationFrameRef = useRef<number>();

  const updateState = useCallback((updates: Partial<RobotAnimationState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const setRobotState = useCallback((newState: RobotState) => {
    const now = Date.now();
    setState(prev => ({
      ...prev,
      previousState: prev.robotState,
      robotState: newState,
      stateStartTime: now,
      // Reset state-specific variables based on new state
      ...(newState === 'dance' && {
        dancePhase: 0,
        danceStartX: prev.robotX,
        danceStartY: prev.robotY,
      }),
      ...(newState === 'search' && {
        searchAngle: 0,
      }),
      ...(newState === 'talk' && {
        targetView: 'front',
        talkPulse: 0,
        mouthAnimation: 0,
        mouthOpenAmount: 0,
      }),
      ...(newState === 'sing' && {
        targetView: 'front',
        talkPulse: 0,
        mouthAnimation: 0,
        mouthOpenAmount: 0,
      }),
      ...(newState === 'walk' && {
        targetView: 'side',
      }),
      ...(newState === 'glamour' && {
        targetView: 'front',
      }),
      ...(newState === 'back' && {
        targetView: 'back',
      }),
      ...(newState === 'trainers' && {
        targetView: 'front',
      }),
      ...(newState === 'roll' && {
        isRolling: true,
        legsVisible: false,
      }),
      ...(newState === 'idle' && !prev.isRolling && {
        targetView: 'front',
      }),
    }));
  }, []);

  const setRobotScale = useCallback((newScale: number) => {
    const clampedScale = Math.max(0.2, Math.min(1.5, newScale));
    updateState({ targetScale: clampedScale });
  }, [updateState]);

  const centerRobot = useCallback(() => {
    setState(prev => ({
      ...prev,
      shouldReturnToCenter: true,
      ...(prev.robotState === 'dance' && {
        danceStartX: prev.centerX,
        danceStartY: prev.centerY,
      }),
    }));
  }, []);

  const rollToPosition = useCallback((x: number, y: number) => {
    updateState({ targetX: x, targetY: y });
    setRobotState('roll');
  }, [updateState, setRobotState]);

  const initializePosition = useCallback((centerX: number, centerY: number) => {
    setState(prev => ({
      ...prev,
      centerX,
      centerY,
      robotX: centerX,
      robotY: centerY,
      targetX: centerX,
      targetY: centerY,
    }));
  }, []);

  const setPosition = useCallback((x: number, y: number) => {
    setState(prev => ({
      ...prev,
      robotX: x,
      robotY: y,
      targetX: x,
      targetY: y,
    }));
  }, []);

  return {
    state,
    updateState,
    setRobotState,
    setRobotScale,
    centerRobot,
    rollToPosition,
    initializePosition,
    setPosition,
  };
};
