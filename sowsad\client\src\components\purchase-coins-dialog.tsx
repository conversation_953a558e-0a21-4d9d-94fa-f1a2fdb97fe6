import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import DasWosCoinIcon from '@/components/shared/daswos-coin-icon';
import { formatDasWosCoins } from '@/lib/utils';

interface PurchaseCoinsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  requiredAmount?: number;
  currentBalance?: number;
  onPurchaseComplete?: () => void;
}

const coinPackages = [
  { id: 1, amount: 100, price: 1, name: "100" },
  { id: 2, amount: 500, price: 5, name: "500" },
  { id: 3, amount: 1000, price: 10, name: "1,000" },
];

const PurchaseCoinsDialog: React.FC<PurchaseCoinsDialogProps> = ({
  isOpen,
  onClose,
  requiredAmount = 0,
  currentBalance = 0,
  onPurchaseComplete
}) => {
  const [selectedPackage, setSelectedPackage] = useState<number | null>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const shortfall = Math.max(0, requiredAmount - currentBalance);

  // Purchase coins mutation
  const purchaseMutation = useMutation({
    mutationFn: async (amount: number) => {
      const response = await fetch('/api/user/daswos-coins/purchase', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          metadata: {
            packageName: `${amount} DasWos Coins`,
            purchaseTimestamp: new Date().toISOString(),
            source: 'ai_buy_insufficient_funds'
          }
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to purchase coins');
      }

      return response.json();
    },
    onSuccess: () => {
      toast({
        title: 'Coins Purchased Successfully!',
        description: `Your DasWos Coins have been added to your account.`,
      });

      // Refresh the balance
      queryClient.invalidateQueries({ queryKey: ['/api/user/daswos-coins/balance'] });

      // Call completion callback
      if (onPurchaseComplete) {
        onPurchaseComplete();
      }

      // Close dialog
      onClose();
    },
    onError: (error) => {
      console.error('Error purchasing coins:', error);
      toast({
        title: 'Purchase Failed',
        description: 'There was an error purchasing coins. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handlePurchase = () => {
    if (!selectedPackage) {
      toast({
        title: 'No package selected',
        description: 'Please select a coin package to purchase.',
        variant: 'destructive',
      });
      return;
    }

    const pkg = coinPackages.find(p => p.id === selectedPackage);
    if (pkg) {
      purchaseMutation.mutate(pkg.amount);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="bg-[#2a2a2a] rounded-lg shadow-xl border border-gray-600 p-4 w-full max-w-sm mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <span className="text-white font-medium">DasWos Coins</span>
        </div>
        <div className="flex items-center gap-1 text-white">
          <DasWosCoinIcon size={16} />
          <span className="font-medium">{currentBalance}</span>
        </div>
      </div>

      {/* Quick Purchase Label */}
      <div className="text-gray-300 text-sm mb-3">
        Quick Purchase
      </div>

      {/* Insufficient funds message if needed */}
      {shortfall > 0 && (
        <div className="text-orange-400 text-xs mb-3 text-center">
          Need {formatDasWosCoins(shortfall)} more to complete purchase
        </div>
      )}

      {/* Coin Package Buttons */}
      <div className="grid grid-cols-3 gap-3 mb-4">
        {coinPackages.map((pkg) => (
          <button
            key={pkg.id}
            onClick={() => setSelectedPackage(pkg.id)}
            className={`relative rounded-lg border-2 p-3 transition-all text-center ${
              selectedPackage === pkg.id
                ? 'border-blue-500 bg-blue-500/10'
                : 'border-gray-600 hover:border-gray-500'
            }`}
          >
            <div className="flex flex-col items-center gap-1">
              <DasWosCoinIcon size={20} className="text-yellow-400" />
              <span className="text-white font-medium text-sm">{pkg.name}</span>
              <span className="text-gray-400 text-xs">${pkg.price}</span>
            </div>
            {selectedPackage === pkg.id && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2">
        <button
          onClick={onClose}
          className="flex-1 py-2 px-4 rounded-lg border border-gray-600 text-gray-300 hover:bg-gray-700 transition-colors text-sm"
        >
          Cancel
        </button>
        <button
          onClick={handlePurchase}
          disabled={!selectedPackage || purchaseMutation.isPending}
          className="flex-1 py-2 px-4 rounded-lg bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white transition-colors text-sm"
        >
          {purchaseMutation.isPending ? 'Purchasing...' : 'Purchase'}
        </button>
      </div>
    </div>
  );
};

export default PurchaseCoinsDialog;
