import React from 'react';
import { Button } from '@/components/ui/button';
import { List, Gift, X } from 'lucide-react';

interface ListSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectList: (listType: 'shopping' | 'wishlist') => void;
  itemName: string;
}

const ListSelectionDialog: React.FC<ListSelectionDialogProps> = ({
  isOpen,
  onClose,
  onSelectList,
  itemName
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6 relative">
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          aria-label="Close"
        >
          <X className="h-5 w-5" />
        </button>
        
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-4">
            <List className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Add to List
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
            Where would you like to add <span className="font-medium">{itemName}</span>?
          </p>
          
          <div className="grid grid-cols-1 gap-3">
            <Button
              onClick={() => onSelectList('shopping')}
              className="justify-start py-6 text-left"
              variant="outline"
            >
              <List className="mr-3 h-5 w-5 text-green-600" />
              <div>
                <div className="font-medium">Shopping List</div>
                <div className="text-xs text-gray-500">For items you plan to purchase</div>
              </div>
            </Button>
            
            <Button
              onClick={() => onSelectList('wishlist')}
              className="justify-start py-6 text-left"
              variant="outline"
            >
              <Gift className="mr-3 h-5 w-5 text-purple-600" />
              <div>
                <div className="font-medium">Wish List</div>
                <div className="text-xs text-gray-500">For items you're interested in</div>
              </div>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListSelectionDialog;
