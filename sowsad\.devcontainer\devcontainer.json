{"name": "Daswos Development", "dockerComposeFile": ["docker-compose.yml", "docker-compose.dev.yml"], "service": "app", "workspaceFolder": "/app", "forwardPorts": [5000, 5432], "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "bradlc.vscode-tailwindcss", "ms-azuretools.vscode-docker", "mtxr.sqltools", "mtxr.sqltools-driver-pg", "yoavbls.pretty-ts-errors"], "settings": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "terminal.integrated.defaultProfile.linux": "bash"}}}, "postCreateCommand": "npm install", "postStartCommand": "npm run dev", "shutdownAction": "stopCompose"}