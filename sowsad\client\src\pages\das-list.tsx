import React, { useState, useEffect } from 'react';
import { List, Plus, Trash2, Edit, Check, X, Gift, ShoppingCart, ShoppingBag } from 'lucide-react';
import { 
  <PERSON>, 
  CardContent, 
  CardHeader, 
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';

type ListItem = {
  id: string;
  name: string;
  completed: boolean;
  createdAt: number;
};

type ListType = {
  id: string;
  name: string;
  items: ListItem[];
  isWishlist: boolean;
  createdAt: number;
};

const DasList: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'shopping' | 'wishlist'>('shopping');
  const [lists, setLists] = useState<ListType[]>([]);
  const [newListName, setNewListName] = useState('');
  const [isCreatingList, setIsCreatingList] = useState(false);
  const [editingListId, setEditingListId] = useState<string | null>(null);
  const [editingListName, setEditingListName] = useState('');
  const [newItemName, setNewItemName] = useState('');
  const [expandedListId, setExpandedListId] = useState<string | null>(null);

  // Load lists from localStorage on component mount
  useEffect(() => {
    const savedLists = localStorage.getItem('das-lists');
    if (savedLists) {
      setLists(JSON.parse(savedLists));
    } else {
      // Initialize with a default shopping list
      const defaultList: ListType = {
        id: '1',
        name: 'My Shopping List',
        items: [],
        isWishlist: false,
        createdAt: Date.now()
      };
      setLists([defaultList]);
    }
  }, []);

  // Save lists to localStorage whenever they change
  useEffect(() => {
    if (lists.length > 0) {
      localStorage.setItem('das-lists', JSON.stringify(lists));
    }
  }, [lists]);

  const createNewList = () => {
    if (!newListName.trim()) return;
    
    const newList: ListType = {
      id: Date.now().toString(),
      name: newListName,
      items: [],
      isWishlist: activeTab === 'wishlist',
      createdAt: Date.now()
    };

    setLists([...lists, newList]);
    setNewListName('');
    setIsCreatingList(false);
  };

  const addItemToList = (listId: string) => {
    if (!newItemName.trim()) return;

    const updatedLists = lists.map(list => {
      if (list.id === listId) {
        const newItem: ListItem = {
          id: Date.now().toString(),
          name: newItemName,
          completed: false,
          createdAt: Date.now()
        };
        return {
          ...list,
          items: [...list.items, newItem]
        };
      }
      return list;
    });

    setLists(updatedLists);
    setNewItemName('');
  };

  const toggleItemComplete = (listId: string, itemId: string) => {
    const updatedLists = lists.map(list => {
      if (list.id === listId) {
        return {
          ...list,
          items: list.items.map(item => 
            item.id === itemId 
              ? { ...item, completed: !item.completed } 
              : item
          )
        };
      }
      return list;
    });

    setLists(updatedLists);
  };

  const deleteItem = (listId: string, itemId: string) => {
    const updatedLists = lists.map(list => {
      if (list.id === listId) {
        return {
          ...list,
          items: list.items.filter(item => item.id !== itemId)
        };
      }
      return list;
    });

    setLists(updatedLists);
  };

  const deleteList = (listId: string) => {
    if (window.confirm('Are you sure you want to delete this list? This cannot be undone.')) {
      setLists(lists.filter(list => list.id !== listId));
      if (expandedListId === listId) {
        setExpandedListId(null);
      }
    }
  };

  const startEditingList = (list: ListType) => {
    setEditingListId(list.id);
    setEditingListName(list.name);
  };

  const saveListName = (listId: string) => {
    if (!editingListName.trim()) return;
    
    const updatedLists = lists.map(list => 
      list.id === listId 
        ? { ...list, name: editingListName } 
        : list
    );
    
    setLists(updatedLists);
    setEditingListId(null);
  };

  const filteredLists = lists.filter(list => 
    activeTab === 'wishlist' ? list.isWishlist : !list.isWishlist
  );

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center mb-6">
          <List className="h-6 w-6 mr-2 text-blue-600" />
          <h1 className="text-2xl font-bold">das.list</h1>
        </div>

        <Tabs 
          defaultValue="shopping" 
          className="w-full"
          onValueChange={(value) => setActiveTab(value as 'shopping' | 'wishlist')}
        >
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="shopping">
              <ShoppingCart className="h-4 w-4 mr-2" />
              Shopping Lists
            </TabsTrigger>
            <TabsTrigger value="wishlist">
              <Gift className="h-4 w-4 mr-2" />
              Wishlist
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab}>
            <div className="space-y-4">
              {isCreatingList ? (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex space-x-2">
                      <Input
                        placeholder="Enter list name"
                        value={newListName}
                        onChange={(e) => setNewListName(e.target.value)}
                        onKeyDown={(e) => e.key === 'Enter' && createNewList()}
                        autoFocus
                      />
                      <Button onClick={createNewList}>
                        <Plus className="h-4 w-4 mr-2" /> Add
                      </Button>
                      <Button 
                        variant="outline" 
                        onClick={() => {
                          setIsCreatingList(false);
                          setNewListName('');
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => setIsCreatingList(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create New {activeTab === 'wishlist' ? 'Wishlist' : 'Shopping List'}
                </Button>
              )}

              <div className="space-y-4">
                {filteredLists.map((list) => (
                  <Card key={list.id}>
                    <CardHeader>
                      <div className="flex justify-between items-center">
                        {editingListId === list.id ? (
                          <div className="flex items-center space-x-2 flex-1">
                            <Input
                              value={editingListName}
                              onChange={(e) => setEditingListName(e.target.value)}
                              onKeyDown={(e) => e.key === 'Enter' && saveListName(list.id)}
                              className="flex-1"
                              autoFocus
                            />
                            <Button 
                              size="sm" 
                              onClick={() => saveListName(list.id)}
                            >
                              <Check className="h-4 w-4" />
                            </Button>
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => setEditingListId(null)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <CardTitle className="text-lg">{list.name}</CardTitle>
                        )}
                        <div className="flex space-x-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => startEditingList(list)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => deleteList(list.id)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      <div className="space-y-2">
                        {list.items.length > 0 ? (
                          <ScrollArea className="h-40">
                            <div className="space-y-2 pr-4">
                              {list.items.map((item) => (
                                <div 
                                  key={item.id} 
                                  className="flex items-center justify-between p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded"
                                >
                                  <div className="flex items-center space-x-2 flex-1">
                                    <input
                                      type="checkbox"
                                      checked={item.completed}
                                      onChange={() => toggleItemComplete(list.id, item.id)}
                                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                    <span className={item.completed ? 'line-through text-gray-500' : ''}>
                                      {item.name}
                                    </span>
                                  </div>
                                  <div className="flex space-x-1">
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        window.open(`https://www.google.com/search?tbm=shop&q=${encodeURIComponent(item.name)}`, '_blank');
                                      }}
                                      title="Search on Google Shopping"
                                    >
                                      <ShoppingBag className="h-4 w-4 text-blue-500" />
                                    </Button>
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        localStorage.setItem('autoStartShopping', 'true');
                                        localStorage.setItem('pendingSearchQuery', item.name);
                                        window.location.href = `/?q=${encodeURIComponent(item.name)}`;
                                      }}
                                      title="Search in Daswos Shopping"
                                    >
                                      <ShoppingBag className="h-4 w-4 text-green-600" />
                                    </Button>
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        deleteItem(list.id, item.id);
                                      }}
                                    >
                                      <X className="h-4 w-4 text-destructive" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </ScrollArea>
                        ) : (
                          <p className="text-sm text-gray-500 italic">
                            No items yet. Add some below!
                          </p>
                        )}
                      </div>

                      <div className="mt-4 flex space-x-2">
                        <Input
                          placeholder={`Add an item to ${list.name}`}
                          value={newItemName}
                          onChange={(e) => setNewItemName(e.target.value)}
                          onKeyDown={(e) => e.key === 'Enter' && addItemToList(list.id)}
                          className="flex-1"
                        />
                        <Button 
                          onClick={() => addItemToList(list.id)}
                          disabled={!newItemName.trim()}
                        >
                          <Plus className="h-4 w-4 mr-2" /> Add
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default DasList;
