<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Hash Generator for DasWos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            word-break: break-all;
        }
        .sql-query {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 DasWos Password Hash Generator</h1>
        
        <div class="info">
            <strong>Purpose:</strong> Generate SHA-256 password hashes compatible with current-brobot-1.<br>
            <strong>Note:</strong> These hashes will work in current-brobot-1 but NOT in daswos-18 (which uses scrypt).
        </div>

        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" placeholder="Enter username">
        </div>

        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" placeholder="Enter password">
        </div>

        <div class="form-group">
            <label for="email">Email:</label>
            <input type="text" id="email" placeholder="Enter email">
        </div>

        <div class="form-group">
            <label for="fullName">Full Name:</label>
            <input type="text" id="fullName" placeholder="Enter full name">
        </div>

        <button onclick="generateHash()">Generate Hash & SQL</button>

        <div id="result" class="result" style="display: none;">
            <h3>Generated Hash:</h3>
            <div id="hashOutput"></div>
            
            <h3>SQL Insert Query:</h3>
            <div id="sqlOutput" class="sql-query"></div>
        </div>
    </div>

    <script>
        // SHA-256 hash function (same as current-brobot-1)
        async function hashPassword(password) {
            const encoder = new TextEncoder();
            const data = encoder.encode(password + 'daswos_salt');
            const hashBuffer = await crypto.subtle.digest('SHA-256', data);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        async function generateHash() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const email = document.getElementById('email').value;
            const fullName = document.getElementById('fullName').value;

            if (!username || !password || !email || !fullName) {
                alert('Please fill in all fields');
                return;
            }

            try {
                const hashedPassword = await hashPassword(password);
                
                document.getElementById('hashOutput').textContent = hashedPassword;
                
                const sqlQuery = `-- Insert user: ${username}
DELETE FROM users WHERE username = '${username}';

INSERT INTO users (
    username, 
    password, 
    email, 
    full_name, 
    is_admin, 
    is_seller, 
    trust_score,
    daswos_coins_balance
) VALUES (
    '${username}', 
    '${hashedPassword}',
    '${email}', 
    '${fullName}', 
    false, 
    false, 
    50,
    1000
);`;

                document.getElementById('sqlOutput').textContent = sqlQuery;
                document.getElementById('result').style.display = 'block';
                
            } catch (error) {
                alert('Error generating hash: ' + error.message);
            }
        }

        // Test with sample data
        window.onload = function() {
            document.getElementById('username').value = 'testuser';
            document.getElementById('password').value = 'password123';
            document.getElementById('email').value = '<EMAIL>';
            document.getElementById('fullName').value = 'Test User';
        };
    </script>
</body>
</html>
